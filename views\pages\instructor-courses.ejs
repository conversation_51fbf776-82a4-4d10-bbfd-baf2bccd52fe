<link rel="stylesheet" href="/css/instructor-courses.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<div class="instructor-courses">
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <h1>My Courses</h1>
      <p>Manage and organize your course content</p>
    </div>
    <div class="header-actions">
      <a href="/instructor/courses/new" class="btn btn-primary">
        <i class="fas fa-plus"></i>
        Create New Course
      </a>
    </div>
  </div>

  <!-- Filters and Search -->
  <div class="filters-section">
    <form class="filters-form" method="GET" action="/instructor/courses">
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input type="text" name="search" placeholder="Search courses..." value="<%= filters.search || '' %>">
      </div>
      
      <div class="filter-group">
        <select name="status" class="filter-select">
          <option value="">All Status</option>
          <option value="draft" <%= filters.status === 'draft' ? 'selected' : '' %>>Draft</option>
          <option value="published" <%= filters.status === 'published' ? 'selected' : '' %>>Published</option>
          <option value="archived" <%= filters.status === 'archived' ? 'selected' : '' %>>Archived</option>
        </select>
      </div>
      
      <div class="filter-group">
        <select name="level" class="filter-select">
          <option value="">All Levels</option>
          <option value="Beginner" <%= filters.level === 'Beginner' ? 'selected' : '' %>>Beginner</option>
          <option value="Intermediate" <%= filters.level === 'Intermediate' ? 'selected' : '' %>>Intermediate</option>
          <option value="Advanced" <%= filters.level === 'Advanced' ? 'selected' : '' %>>Advanced</option>
        </select>
      </div>
      
      <% if (categories && categories.length > 0) { %>
        <div class="filter-group">
          <select name="category" class="filter-select">
            <option value="">All Categories</option>
            <% categories.forEach(category => { %>
              <option value="<%= category %>" <%= filters.category === category ? 'selected' : '' %>>
                <%= category %>
              </option>
            <% }); %>
          </select>
        </div>
      <% } %>
      
      <button type="submit" class="btn btn-secondary">
        <i class="fas fa-filter"></i>
        Filter
      </button>
      
      <a href="/instructor/courses" class="btn btn-outline">
        <i class="fas fa-times"></i>
        Clear
      </a>
    </form>
  </div>

  <!-- Success/Error Messages -->
  <% if (typeof success !== 'undefined' && success) { %>
    <div class="alert alert-success">
      <i class="fas fa-check-circle"></i>
      <%= success %>
    </div>
  <% } %>
  
  <% if (typeof error !== 'undefined' && error) { %>
    <div class="alert alert-error">
      <i class="fas fa-exclamation-circle"></i>
      <%= error %>
    </div>
  <% } %>

  <!-- Courses Grid -->
  <div class="courses-section">
    <% if (courses && courses.length > 0) { %>
      <div class="courses-grid">
        <% courses.forEach(course => { %>
          <div class="course-card" data-course-id="<%= course._id %>">
            <div class="course-image">
              <% if (course.imageUrl) { %>
                <img src="<%= course.imageUrl %>" alt="<%= course.title %>" loading="lazy">
              <% } else { %>
                <div class="image-placeholder">
                  <i class="fas fa-book"></i>
                </div>
              <% } %>
              <div class="course-status-badge status-<%= course.status %>">
                <%= course.status.charAt(0).toUpperCase() + course.status.slice(1) %>
              </div>
            </div>
            
            <div class="course-content">
              <div class="course-header">
                <h3 class="course-title"><%= course.title %></h3>
                <div class="course-level level-<%= course.level.toLowerCase() %>">
                  <%= course.level %>
                </div>
              </div>
              
              <p class="course-description">
                <%= course.description.length > 100 ? course.description.substring(0, 100) + '...' : course.description %>
              </p>
              
              <div class="course-meta">
                <div class="meta-item">
                  <i class="fas fa-clock"></i>
                  <span><%= course.duration %> hours</span>
                </div>
                <div class="meta-item">
                  <i class="fas fa-users"></i>
                  <span><%= course.enrollmentCount || 0 %> students</span>
                </div>
                <div class="meta-item">
                  <i class="fas fa-star"></i>
                  <span><%= (course.rating || 0).toFixed(1) %></span>
                </div>
                <div class="meta-item">
                  <i class="fas fa-dollar-sign"></i>
                  <span>$<%= course.price %></span>
                </div>
              </div>
              
              <% if (course.tags && course.tags.length > 0) { %>
                <div class="course-tags">
                  <% course.tags.slice(0, 3).forEach(tag => { %>
                    <span class="tag"><%= tag %></span>
                  <% }); %>
                  <% if (course.tags.length > 3) { %>
                    <span class="tag-more">+<%= course.tags.length - 3 %></span>
                  <% } %>
                </div>
              <% } %>
              
              <div class="course-actions">
                <a href="/instructor/courses/<%= course._id %>/edit" class="btn btn-sm btn-primary">
                  <i class="fas fa-edit"></i>
                  Edit
                </a>
                <button class="btn btn-sm btn-outline" onclick="viewCourse('<%= course._id %>')">
                  <i class="fas fa-eye"></i>
                  View
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteCourse('<%= course._id %>', '<%= course.title %>')">
                  <i class="fas fa-trash"></i>
                  Delete
                </button>
              </div>
            </div>
          </div>
        <% }); %>
      </div>
      
      <!-- Pagination -->
      <% if (pagination.total > 1) { %>
        <div class="pagination">
          <% if (pagination.hasPrev) { %>
            <a href="?page=<%= pagination.current - 1 %><%= Object.keys(filters).map(key => filters[key] ? `&${key}=${encodeURIComponent(filters[key])}` : '').join('') %>" class="pagination-btn">
              <i class="fas fa-chevron-left"></i>
              Previous
            </a>
          <% } %>
          
          <div class="pagination-info">
            Page <%= pagination.current %> of <%= pagination.total %>
          </div>
          
          <% if (pagination.hasNext) { %>
            <a href="?page=<%= pagination.current + 1 %><%= Object.keys(filters).map(key => filters[key] ? `&${key}=${encodeURIComponent(filters[key])}` : '').join('') %>" class="pagination-btn">
              Next
              <i class="fas fa-chevron-right"></i>
            </a>
          <% } %>
        </div>
      <% } %>
      
    <% } else { %>
      <div class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-book-open"></i>
        </div>
        <h3>No courses found</h3>
        <% if (Object.values(filters).some(filter => filter)) { %>
          <p>No courses match your current filters. Try adjusting your search criteria.</p>
          <a href="/instructor/courses" class="btn btn-secondary">
            <i class="fas fa-times"></i>
            Clear Filters
          </a>
        <% } else { %>
          <p>You haven't created any courses yet. Start sharing your knowledge with students!</p>
          <a href="/instructor/courses/new" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            Create Your First Course
          </a>
        <% } %>
      </div>
    <% } %>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="modal">
  <div class="modal-content">
    <div class="modal-header">
      <h3>Confirm Deletion</h3>
      <button class="modal-close" onclick="closeDeleteModal()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="modal-body">
      <p>Are you sure you want to delete the course "<span id="courseToDelete"></span>"?</p>
      <p class="warning-text">This action cannot be undone.</p>
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary" onclick="closeDeleteModal()">Cancel</button>
      <button class="btn btn-danger" onclick="confirmDelete()">Delete Course</button>
    </div>
  </div>
</div>

<script src="/js/instructor-courses.js"></script>
