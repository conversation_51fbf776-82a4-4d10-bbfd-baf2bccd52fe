/**
 * @fileoverview Application constants and enums
 * @module utils/constants
 */

/**
 * User roles enumeration
 * @readonly
 * @enum {string}
 */
export const USER_ROLES = {
  STUDENT: 'student',
  INSTRUCTOR: 'instructor',
  MANAGER: 'manager'
};

/**
 * Course levels enumeration
 * @readonly
 * @enum {string}
 */
export const COURSE_LEVELS = {
  BEGINNER: 'Beginner',
  INTERMEDIATE: 'Intermediate',
  ADVANCED: 'Advanced'
};

/**
 * Course status enumeration
 * @readonly
 * @enum {string}
 */
export const COURSE_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  ARCHIVED: 'archived'
};

/**
 * Authentication types enumeration
 * @readonly
 * @enum {string}
 */
export const AUTH_TYPES = {
  LOCAL: 'local',
  GOOGLE: 'google'
};

/**
 * Material types enumeration
 * @readonly
 * @enum {string}
 */
export const MATERIAL_TYPES = {
  VIDEO: 'video',
  DOCUMENT: 'document',
  LINK: 'link',
  QUIZ: 'quiz'
};

/**
 * HTTP status codes
 * @readonly
 * @enum {number}
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500
};

/**
 * Default pagination settings
 * @readonly
 * @type {Object}
 */
export const PAGINATION_DEFAULTS = {
  PAGE: 1,
  LIMIT: 10,
  MAX_LIMIT: 100
};

/**
 * File upload constraints
 * @readonly
 * @type {Object}
 */
export const UPLOAD_CONSTRAINTS = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
};

/**
 * Validation constraints
 * @readonly
 * @type {Object}
 */
export const VALIDATION_CONSTRAINTS = {
  USER: {
    NAME_MIN_LENGTH: 2,
    NAME_MAX_LENGTH: 50,
    PASSWORD_MIN_LENGTH: 6,
    AGE_MIN: 0,
    AGE_MAX: 120
  },
  COURSE: {
    TITLE_MAX_LENGTH: 100,
    DESCRIPTION_MAX_LENGTH: 2000,
    CATEGORY_MAX_LENGTH: 50,
    DURATION_MIN: 0.5,
    DURATION_MAX: 500,
    MAX_TAGS: 10,
    PRICE_MIN: 0
  }
};

/**
 * JWT token settings
 * @readonly
 * @type {Object}
 */
export const JWT_SETTINGS = {
  ACCESS_TOKEN_EXPIRY: '15m',
  REFRESH_TOKEN_EXPIRY: '7d',
  COOKIE_MAX_AGE: 15 * 60 * 1000 // 15 minutes in milliseconds
};

/**
 * Database collection names
 * @readonly
 * @type {Object}
 */
export const COLLECTIONS = {
  USERS: 'users',
  COURSES: 'courses',
  ENROLLMENTS: 'enrollments'
};

/**
 * Error messages
 * @readonly
 * @type {Object}
 */
export const ERROR_MESSAGES = {
  // Authentication errors
  INVALID_CREDENTIALS: 'Invalid email or password',
  TOKEN_EXPIRED: 'Token has expired',
  TOKEN_INVALID: 'Invalid token',
  ACCESS_DENIED: 'Access denied',
  UNAUTHORIZED: 'Authentication required',
  
  // Validation errors
  REQUIRED_FIELDS_MISSING: 'Required fields are missing',
  INVALID_EMAIL: 'Invalid email format',
  INVALID_PASSWORD: 'Password does not meet requirements',
  INVALID_ID: 'Invalid ID format',
  
  // Resource errors
  USER_NOT_FOUND: 'User not found',
  COURSE_NOT_FOUND: 'Course not found',
  RESOURCE_NOT_FOUND: 'Resource not found',
  DUPLICATE_EMAIL: 'Email already exists',
  
  // Permission errors
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions',
  COURSE_ACCESS_DENIED: 'You do not have access to this course',
  
  // General errors
  INTERNAL_ERROR: 'Internal server error',
  DATABASE_ERROR: 'Database operation failed',
  FILE_UPLOAD_ERROR: 'File upload failed'
};

/**
 * Success messages
 * @readonly
 * @type {Object}
 */
export const SUCCESS_MESSAGES = {
  // User operations
  USER_CREATED: 'User created successfully',
  USER_UPDATED: 'User updated successfully',
  USER_DELETED: 'User deleted successfully',
  PROFILE_UPDATED: 'Profile updated successfully',
  PASSWORD_CHANGED: 'Password changed successfully',
  
  // Authentication
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  
  // Course operations
  COURSE_CREATED: 'Course created successfully',
  COURSE_UPDATED: 'Course updated successfully',
  COURSE_DELETED: 'Course deleted successfully',
  COURSE_PUBLISHED: 'Course published successfully',
  
  // General
  OPERATION_SUCCESS: 'Operation completed successfully',
  DATA_RETRIEVED: 'Data retrieved successfully'
};

/**
 * Regular expressions for validation
 * @readonly
 * @type {Object}
 */
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^[\+]?[1-9][\d]{0,15}$/,
  OBJECT_ID: /^[0-9a-fA-F]{24}$/,
  PASSWORD_STRONG: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/
};

/**
 * Environment configurations
 * @readonly
 * @type {Object}
 */
export const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
};

/**
 * Cache settings
 * @readonly
 * @type {Object}
 */
export const CACHE_SETTINGS = {
  DEFAULT_TTL: 300, // 5 minutes
  USER_PROFILE_TTL: 600, // 10 minutes
  COURSE_LIST_TTL: 180, // 3 minutes
  STATS_TTL: 900 // 15 minutes
};
