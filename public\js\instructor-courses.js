// Instructor Courses JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeCoursesPage();
});

let courseToDeleteId = null;

function initializeCoursesPage() {
    // Initialize search functionality
    initializeSearch();
    
    // Initialize filter functionality
    initializeFilters();
    
    // Initialize course actions
    initializeCourseActions();
    
    // Initialize modal
    initializeModal();
    
    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
    
    // Auto-save search and filter state
    saveFilterState();
}

function initializeSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    if (!searchInput) return;
    
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            // Auto-submit search after 500ms of no typing
            if (this.value.length >= 3 || this.value.length === 0) {
                this.form.submit();
            }
        }, 500);
    });
    
    // Clear search button
    const clearSearchBtn = document.createElement('button');
    clearSearchBtn.type = 'button';
    clearSearchBtn.className = 'clear-search-btn';
    clearSearchBtn.innerHTML = '<i class="fas fa-times"></i>';
    clearSearchBtn.onclick = () => {
        searchInput.value = '';
        searchInput.form.submit();
    };
    
    if (searchInput.value) {
        searchInput.parentNode.appendChild(clearSearchBtn);
    }
    
    searchInput.addEventListener('input', function() {
        if (this.value) {
            if (!this.parentNode.querySelector('.clear-search-btn')) {
                this.parentNode.appendChild(clearSearchBtn);
            }
        } else {
            const existingBtn = this.parentNode.querySelector('.clear-search-btn');
            if (existingBtn) {
                existingBtn.remove();
            }
        }
    });
}

function initializeFilters() {
    const filterSelects = document.querySelectorAll('.filter-select');
    
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            // Auto-submit when filter changes
            this.form.submit();
        });
    });
}

function initializeCourseActions() {
    // Add hover effects to course cards
    const courseCards = document.querySelectorAll('.course-card');
    
    courseCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-4px)';
        });
    });
}

function initializeModal() {
    const modal = document.getElementById('deleteModal');
    if (!modal) return;
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeDeleteModal();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.style.display === 'block') {
            closeDeleteModal();
        }
    });
}

function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + N for new course
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            window.location.href = '/instructor/courses/new';
        }
        
        // Ctrl/Cmd + F for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.querySelector('input[name="search"]');
            if (searchInput) {
                searchInput.focus();
            }
        }
    });
}

function saveFilterState() {
    // Save current filter state to localStorage
    const form = document.querySelector('.filters-form');
    if (!form) return;
    
    const formData = new FormData(form);
    const filters = {};
    
    for (let [key, value] of formData.entries()) {
        if (value) filters[key] = value;
    }
    
    localStorage.setItem('instructorCoursesFilters', JSON.stringify(filters));
}

// Course action functions
function viewCourse(courseId) {
    window.open(`/courses/${courseId}`, '_blank');
}

function deleteCourse(courseId, courseTitle) {
    courseToDeleteId = courseId;
    document.getElementById('courseToDelete').textContent = courseTitle;
    document.getElementById('deleteModal').style.display = 'block';
}

function closeDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
    courseToDeleteId = null;
}

async function confirmDelete() {
    if (!courseToDeleteId) return;
    
    const deleteBtn = document.querySelector('.modal-footer .btn-danger');
    const originalText = deleteBtn.innerHTML;
    
    try {
        // Show loading state
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
        deleteBtn.disabled = true;
        
        const response = await fetch(`/instructor/courses/${courseToDeleteId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getCookie('accessToken')}`
            },
            credentials: 'include'
        });
        
        const result = await response.json();
        
        if (response.ok && result.success) {
            // Remove course card from DOM
            const courseCard = document.querySelector(`[data-course-id="${courseToDeleteId}"]`);
            if (courseCard) {
                courseCard.style.transition = 'all 0.3s ease';
                courseCard.style.opacity = '0';
                courseCard.style.transform = 'scale(0.8)';
                
                setTimeout(() => {
                    courseCard.remove();
                    
                    // Check if no courses left
                    const remainingCourses = document.querySelectorAll('.course-card');
                    if (remainingCourses.length === 0) {
                        showEmptyState();
                    }
                }, 300);
            }
            
            showMessage('success', result.message || 'Course deleted successfully');
            closeDeleteModal();
        } else {
            throw new Error(result.message || 'Failed to delete course');
        }
    } catch (error) {
        console.error('Delete error:', error);
        showMessage('error', error.message || 'Failed to delete course');
    } finally {
        // Restore button state
        deleteBtn.innerHTML = originalText;
        deleteBtn.disabled = false;
    }
}

function showEmptyState() {
    const coursesGrid = document.querySelector('.courses-grid');
    if (!coursesGrid) return;
    
    coursesGrid.innerHTML = `
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-book-open"></i>
            </div>
            <h3>No courses found</h3>
            <p>You haven't created any courses yet. Start sharing your knowledge with students!</p>
            <a href="/instructor/courses/new" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Create Your First Course
            </a>
        </div>
    `;
}

// Utility functions
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
}

function showMessage(type, message) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        ${message}
    `;
    
    // Insert after page header
    const pageHeader = document.querySelector('.page-header');
    pageHeader.insertAdjacentElement('afterend', alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        alertDiv.style.transition = 'all 0.3s ease';
        alertDiv.style.opacity = '0';
        alertDiv.style.transform = 'translateY(-10px)';
        
        setTimeout(() => {
            alertDiv.remove();
        }, 300);
    }, 5000);
}

// Bulk actions (future enhancement)
function initializeBulkActions() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const courseCheckboxes = document.querySelectorAll('.course-checkbox');
    const bulkActionsBar = document.querySelector('.bulk-actions');
    
    if (!selectAllCheckbox) return;
    
    selectAllCheckbox.addEventListener('change', function() {
        courseCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        toggleBulkActions();
    });
    
    courseCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', toggleBulkActions);
    });
    
    function toggleBulkActions() {
        const selectedCourses = document.querySelectorAll('.course-checkbox:checked');
        if (selectedCourses.length > 0) {
            bulkActionsBar.style.display = 'flex';
        } else {
            bulkActionsBar.style.display = 'none';
        }
    }
}

// Course status quick update
async function updateCourseStatus(courseId, newStatus) {
    try {
        const response = await fetch(`/instructor/courses/${courseId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getCookie('accessToken')}`
            },
            credentials: 'include',
            body: JSON.stringify({ status: newStatus })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            // Update status badge in DOM
            const statusBadge = document.querySelector(`[data-course-id="${courseId}"] .course-status-badge`);
            if (statusBadge) {
                statusBadge.className = `course-status-badge status-${newStatus}`;
                statusBadge.textContent = newStatus.charAt(0).toUpperCase() + newStatus.slice(1);
            }
            
            showMessage('success', 'Course status updated successfully');
        } else {
            throw new Error(result.message || 'Failed to update course status');
        }
    } catch (error) {
        console.error('Status update error:', error);
        showMessage('error', error.message || 'Failed to update course status');
    }
}

// Add CSS for additional styling
const additionalStyles = document.createElement('style');
additionalStyles.textContent = `
    .clear-search-btn {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.5);
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 4px;
        transition: all 0.3s ease;
    }
    
    .clear-search-btn:hover {
        color: #ffffff;
        background: rgba(255, 255, 255, 0.1);
    }
    
    .search-box {
        position: relative;
    }
    
    .course-card {
        transition: all 0.3s ease;
    }
    
    .bulk-actions {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(26, 26, 46, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 1rem;
        display: none;
        gap: 1rem;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }
    
    .status-dropdown {
        position: relative;
        display: inline-block;
    }
    
    .status-dropdown-content {
        display: none;
        position: absolute;
        background: rgba(26, 26, 46, 0.95);
        min-width: 120px;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
        border-radius: 4px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        z-index: 1;
    }
    
    .status-dropdown:hover .status-dropdown-content {
        display: block;
    }
    
    .status-option {
        color: #ffffff;
        padding: 8px 12px;
        text-decoration: none;
        display: block;
        font-size: 0.85rem;
        cursor: pointer;
        transition: background 0.3s ease;
    }
    
    .status-option:hover {
        background: rgba(255, 255, 255, 0.1);
    }
`;

document.head.appendChild(additionalStyles);
