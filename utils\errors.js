/**
 * @fileoverview Custom error classes and error handling utilities
 * @module utils/errors
 */

/**
 * Base application error class
 * @class AppError
 * @extends Error
 */
class AppError extends Error {
  /**
   * Create an application error
   * @param {string} message - Error message
   * @param {number} statusCode - HTTP status code
   * @param {boolean} isOperational - Whether error is operational (expected)
   */
  constructor(message, statusCode, isOperational = true) {
    super(message);
    
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = isOperational;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Validation error class
 * @class ValidationError
 * @extends AppError
 */
class ValidationError extends AppError {
  /**
   * Create a validation error
   * @param {string} message - Validation error message
   * @param {Object} [details] - Additional validation details
   */
  constructor(message, details = null) {
    super(message, 400);
    this.name = 'ValidationError';
    this.details = details;
  }
}

/**
 * Authentication error class
 * @class AuthenticationError
 * @extends AppError
 */
class AuthenticationError extends AppError {
  /**
   * Create an authentication error
   * @param {string} [message='Authentication failed'] - Error message
   */
  constructor(message = 'Authentication failed') {
    super(message, 401);
    this.name = 'AuthenticationError';
  }
}

/**
 * Authorization error class
 * @class AuthorizationError
 * @extends AppError
 */
class AuthorizationError extends AppError {
  /**
   * Create an authorization error
   * @param {string} [message='Access denied'] - Error message
   */
  constructor(message = 'Access denied') {
    super(message, 403);
    this.name = 'AuthorizationError';
  }
}

/**
 * Not found error class
 * @class NotFoundError
 * @extends AppError
 */
class NotFoundError extends AppError {
  /**
   * Create a not found error
   * @param {string} [message='Resource not found'] - Error message
   */
  constructor(message = 'Resource not found') {
    super(message, 404);
    this.name = 'NotFoundError';
  }
}

/**
 * Conflict error class
 * @class ConflictError
 * @extends AppError
 */
class ConflictError extends AppError {
  /**
   * Create a conflict error
   * @param {string} [message='Resource conflict'] - Error message
   */
  constructor(message = 'Resource conflict') {
    super(message, 409);
    this.name = 'ConflictError';
  }
}

/**
 * Database error class
 * @class DatabaseError
 * @extends AppError
 */
class DatabaseError extends AppError {
  /**
   * Create a database error
   * @param {string} [message='Database operation failed'] - Error message
   * @param {Error} [originalError] - Original database error
   */
  constructor(message = 'Database operation failed', originalError = null) {
    super(message, 500);
    this.name = 'DatabaseError';
    this.originalError = originalError;
  }
}

/**
 * Handle Mongoose cast errors
 * @param {Error} err - Mongoose cast error
 * @returns {ValidationError} Formatted validation error
 */
const handleCastErrorDB = (err) => {
  const message = `Invalid ${err.path}: ${err.value}`;
  return new ValidationError(message);
};

/**
 * Handle Mongoose duplicate field errors
 * @param {Error} err - Mongoose duplicate error
 * @returns {ConflictError} Formatted conflict error
 */
const handleDuplicateFieldsDB = (err) => {
  const value = err.errmsg.match(/(["'])(\\?.)*?\1/)[0];
  const message = `Duplicate field value: ${value}. Please use another value!`;
  return new ConflictError(message);
};

/**
 * Handle Mongoose validation errors
 * @param {Error} err - Mongoose validation error
 * @returns {ValidationError} Formatted validation error
 */
const handleValidationErrorDB = (err) => {
  const errors = Object.values(err.errors).map(el => el.message);
  const message = `Invalid input data. ${errors.join('. ')}`;
  return new ValidationError(message, errors);
};

/**
 * Handle JWT errors
 * @param {Error} err - JWT error
 * @returns {AuthenticationError} Formatted authentication error
 */
const handleJWTError = () => 
  new AuthenticationError('Invalid token. Please log in again!');

/**
 * Handle JWT expired errors
 * @param {Error} err - JWT expired error
 * @returns {AuthenticationError} Formatted authentication error
 */
const handleJWTExpiredError = () => 
  new AuthenticationError('Your token has expired! Please log in again.');

/**
 * Send error response in development
 * @param {Error} err - Error object
 * @param {Object} res - Express response object
 */
const sendErrorDev = (err, res) => {
  res.status(err.statusCode).json({
    status: err.status,
    error: err,
    message: err.message,
    stack: err.stack
  });
};

/**
 * Send error response in production
 * @param {Error} err - Error object
 * @param {Object} res - Express response object
 */
const sendErrorProd = (err, res) => {
  // Operational, trusted error: send message to client
  if (err.isOperational) {
    res.status(err.statusCode).json({
      status: err.status,
      message: err.message
    });
  } else {
    // Programming or other unknown error: don't leak error details
    console.error('ERROR 💥', err);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong!'
    });
  }
};

/**
 * Global error handling middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const globalErrorHandler = (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';

  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(err, res);
  } else {
    let error = { ...err };
    error.message = err.message;

    if (error.name === 'CastError') error = handleCastErrorDB(error);
    if (error.code === 11000) error = handleDuplicateFieldsDB(error);
    if (error.name === 'ValidationError') error = handleValidationErrorDB(error);
    if (error.name === 'JsonWebTokenError') error = handleJWTError();
    if (error.name === 'TokenExpiredError') error = handleJWTExpiredError();

    sendErrorProd(error, res);
  }
};

/**
 * Async error handler wrapper
 * @param {Function} fn - Async function to wrap
 * @returns {Function} Express middleware function
 */
const catchAsync = (fn) => {
  return (req, res, next) => {
    fn(req, res, next).catch(next);
  };
};

export {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  DatabaseError,
  globalErrorHandler,
  catchAsync
};
