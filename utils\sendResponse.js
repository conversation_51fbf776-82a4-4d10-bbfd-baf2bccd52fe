/**
 * @fileoverview Response utilities for consistent API responses
 * @module utils/sendResponse
 */

/**
 * Send standardized JSON response
 * @param {Object} res - Express response object
 * @param {number} statusCode - HTTP status code
 * @param {string} message - Response message
 * @param {*} [data=null] - Response data
 * @param {Object} [meta=null] - Additional metadata (pagination, etc.)
 * @returns {Object} Express response
 *
 * @example
 * // Success response with data
 * sendResponse(res, 200, 'User retrieved successfully', user);
 *
 * @example
 * // Success response with pagination
 * sendResponse(res, 200, 'Users retrieved', users, {
 *   page: 1,
 *   totalPages: 5,
 *   total: 50
 * });
 */
export const sendResponse = (res, statusCode, message, data = null, meta = null) => {
  const response = {
    success: statusCode >= 200 && statusCode < 300,
    message,
    timestamp: new Date().toISOString()
  };

  if (data !== null) {
    response.data = data;
  }

  if (meta !== null) {
    response.meta = meta;
  }

  return res.status(statusCode).json(response);
};

/**
 * Send success response
 * @param {Object} res - Express response object
 * @param {string} message - Success message
 * @param {*} [data=null] - Response data
 * @param {Object} [meta=null] - Additional metadata
 * @returns {Object} Express response
 *
 * @example
 * sendSuccess(res, 'User created successfully', user);
 */
export const sendSuccess = (res, message, data = null, meta = null) => {
  return sendResponse(res, 200, message, data, meta);
};

/**
 * Send created response
 * @param {Object} res - Express response object
 * @param {string} message - Success message
 * @param {*} [data=null] - Response data
 * @returns {Object} Express response
 *
 * @example
 * sendCreated(res, 'Course created successfully', course);
 */
export const sendCreated = (res, message, data = null) => {
  return sendResponse(res, 201, message, data);
};

/**
 * Send no content response
 * @param {Object} res - Express response object
 * @returns {Object} Express response
 *
 * @example
 * sendNoContent(res); // For successful DELETE operations
 */
export const sendNoContent = (res) => {
  return res.status(204).send();
};

/**
 * Send error response
 * @param {Object} res - Express response object
 * @param {number} statusCode - HTTP status code
 * @param {string} message - Error message
 * @param {*} [errors=null] - Detailed error information
 * @returns {Object} Express response
 *
 * @example
 * sendError(res, 400, 'Validation failed', validationErrors);
 */
export const sendError = (res, statusCode, message, errors = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };

  if (errors !== null) {
    response.errors = errors;
  }

  return res.status(statusCode).json(response);
};

/**
 * Send paginated response
 * @param {Object} res - Express response object
 * @param {string} message - Response message
 * @param {Array} data - Array of items
 * @param {Object} pagination - Pagination information
 * @param {number} pagination.page - Current page
 * @param {number} pagination.limit - Items per page
 * @param {number} pagination.total - Total items
 * @param {number} pagination.totalPages - Total pages
 * @returns {Object} Express response
 *
 * @example
 * sendPaginated(res, 'Courses retrieved', courses, {
 *   page: 1,
 *   limit: 10,
 *   total: 100,
 *   totalPages: 10
 * });
 */
export const sendPaginated = (res, message, data, pagination) => {
  const meta = {
    pagination: {
      currentPage: pagination.page,
      itemsPerPage: pagination.limit,
      totalItems: pagination.total,
      totalPages: pagination.totalPages,
      hasNextPage: pagination.page < pagination.totalPages,
      hasPrevPage: pagination.page > 1
    }
  };

  return sendResponse(res, 200, message, data, meta);
};
