/**
 * @fileoverview Middleware configuration and setup
 * @module config/middleware
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import path from 'path';
import { fileURLToPath } from 'url';
import { globalErrorHandler } from '../utils/errors.js';
import config from './environment.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Configure CORS options
 * @function configureCors
 * @returns {Object} CORS configuration
 */
const configureCors = () => {
  const corsOptions = {
    origin: (origin, callback) => {
      // Allow requests with no origin (mobile apps, Postman, etc.)
      if (!origin) return callback(null, true);
      
      // In development, allow all origins
      if (config.app.environment === 'development') {
        return callback(null, true);
      }
      
      // In production, check against allowed origins
      const allowedOrigins = config.security.corsOrigin.split(',');
      if (allowedOrigins.includes(origin) || allowedOrigins.includes('*')) {
        return callback(null, true);
      }
      
      return callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposedHeaders: ['X-Total-Count', 'X-Page-Count']
  };

  return corsOptions;
};

/**
 * Configure rate limiting
 * @function configureRateLimit
 * @returns {Function} Rate limiting middleware
 */
const configureRateLimit = () => {
  if (!config.features.enableRateLimiting) {
    return (req, res, next) => next();
  }

  return rateLimit({
    windowMs: config.security.rateLimitWindowMs,
    max: config.security.rateLimitMaxRequests,
    message: {
      success: false,
      message: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.ceil(config.security.rateLimitWindowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for health checks
      return req.path === '/health';
    }
  });
};

/**
 * Configure security headers
 * @function configureSecurity
 * @returns {Function} Helmet middleware
 */
const configureSecurity = () => {
  return helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
        scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
        imgSrc: ["'self'", "data:", "https:"],
        fontSrc: ["'self'", "https://cdnjs.cloudflare.com"],
        connectSrc: ["'self'"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        upgradeInsecureRequests: config.app.environment === 'production' ? [] : null
      }
    },
    crossOriginEmbedderPolicy: false,
    crossOriginResourcePolicy: { policy: "cross-origin" }
  });
};

/**
 * Configure logging middleware
 * @function configureLogging
 * @returns {Function} Morgan middleware
 */
const configureLogging = () => {
  const format = config.app.environment === 'production' ? 'combined' : 'dev';
  
  return morgan(format, {
    skip: (req, res) => {
      // Skip logging for health checks in production
      return config.app.environment === 'production' && req.path === '/health';
    }
  });
};

/**
 * Configure compression middleware
 * @function configureCompression
 * @returns {Function} Compression middleware
 */
const configureCompression = () => {
  return compression({
    filter: (req, res) => {
      // Don't compress responses if the request includes a cache-control no-transform directive
      if (req.headers['cache-control'] && req.headers['cache-control'].includes('no-transform')) {
        return false;
      }
      
      // Use compression filter function
      return compression.filter(req, res);
    },
    level: 6, // Compression level (1-9)
    threshold: 1024 // Only compress responses larger than 1KB
  });
};

/**
 * Setup application middleware
 * @function setupMiddleware
 * @param {Object} app - Express application instance
 * 
 * @example
 * import { setupMiddleware } from './config/middleware.js';
 * setupMiddleware(app);
 */
export const setupMiddleware = (app) => {
  // Trust proxy if configured
  if (config.security.trustProxy) {
    app.set('trust proxy', 1);
  }

  // Security middleware (should be first)
  app.use(configureSecurity());

  // CORS middleware
  app.use(cors(configureCors()));

  // Rate limiting
  app.use(configureRateLimit());

  // Compression middleware
  app.use(configureCompression());

  // Logging middleware
  app.use(configureLogging());

  // Body parsing middleware
  app.use(express.json({ 
    limit: '10mb',
    verify: (req, res, buf) => {
      // Store raw body for webhook verification if needed
      req.rawBody = buf;
    }
  }));
  
  app.use(express.urlencoded({ 
    extended: true, 
    limit: '10mb' 
  }));

  // Cookie parsing middleware
  app.use(cookieParser());

  // Method override middleware for HTML forms
  app.use((req, res, next) => {
    if (req.body && typeof req.body === 'object' && '_method' in req.body) {
      const method = req.body._method;
      delete req.body._method;
      req.method = method;
    }
    next();
  });

  // Static file serving
  const publicPath = path.join(__dirname, '..', 'public');
  app.use(express.static(publicPath, {
    maxAge: config.app.environment === 'production' ? '1d' : '0',
    etag: true,
    lastModified: true
  }));

  // View engine setup (if using EJS)
  const viewsPath = path.join(__dirname, '..', 'views');
  app.set('views', viewsPath);
  app.set('view engine', 'ejs');

  console.log('✅ Middleware configured successfully');
};

/**
 * Setup error handling middleware (should be last)
 * @function setupErrorHandling
 * @param {Object} app - Express application instance
 * 
 * @example
 * import { setupErrorHandling } from './config/middleware.js';
 * setupErrorHandling(app);
 */
export const setupErrorHandling = (app) => {
  // 404 handler for web routes
  app.use((req, res, next) => {
    if (req.path.startsWith('/api/')) {
      return next(); // Let API 404 handler deal with API routes
    }
    
    res.status(404).render('pages/404', {
      title: 'Page Not Found | JooCourses',
      message: 'The page you are looking for does not exist.'
    });
  });

  // Global error handler
  app.use(globalErrorHandler);

  console.log('✅ Error handling configured successfully');
};

/**
 * Configure development-specific middleware
 * @function setupDevelopmentMiddleware
 * @param {Object} app - Express application instance
 */
export const setupDevelopmentMiddleware = (app) => {
  if (config.app.environment !== 'development') {
    return;
  }

  // Development-specific middleware can be added here
  console.log('🔧 Development middleware configured');
};

/**
 * Configure production-specific middleware
 * @function setupProductionMiddleware
 * @param {Object} app - Express application instance
 */
export const setupProductionMiddleware = (app) => {
  if (config.app.environment !== 'production') {
    return;
  }

  // Production-specific middleware can be added here
  // For example: additional security headers, monitoring, etc.
  console.log('🚀 Production middleware configured');
};

export default {
  setupMiddleware,
  setupErrorHandling,
  setupDevelopmentMiddleware,
  setupProductionMiddleware
};
