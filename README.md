# 📚 Courses Platform Backend

This is the backend server for the **Courses Platform** — a full-featured learning management system built using **Node.js**, **Express**, and **MongoDB**. It supports authentication, course management, user roles, file uploads, and more.

---

## 🚀 Features

- ✅ User authentication (JWT + Google OAuth)
- 👤 User roles (Admin, Instructor, Student)
- 🎓 Course creation, update, and deletion
- 📦 RESTful APIs
- 🖼️ File upload support (images, videos, PDFs using Multer)
- 🔒 Secure route protection and input validation
- 🌍 EJS frontend integration for views 

---

## 🚧 Still in Progress

- Creating full Admin and Instructor APIs
- Building dashboard pages for each role
- Designing and developing the enrollment lifecycle
- Finalizing role-based views and permissions

---

## 🧱 Tech Stack

- **Backend**: Node.js, Express
- **Database**: MongoDB + Mongoose
- **Authentication**: JWT + Cookies + Google OAuth
- **File Upload**: Multer
- **Templating**: EJS
- **Environment**: dotenv
