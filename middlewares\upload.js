/**
 * @fileoverview File upload middleware using multer
 * @module middlewares/upload
 */

import multer from "multer";
import path from "path";
import fs from "fs";

/**
 * Upload directory path
 * @type {string}
 */
const uploadDir = "uploads";

/**
 * Ensure upload directory exists
 */
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

/**
 * Multer storage configuration
 * @type {Object}
 */
const storage = multer.diskStorage({
  /**
   * Set destination directory for uploaded files
   * @param {Object} _req - Express request object (unused)
   * @param {Object} _file - File object (unused)
   * @param {Function} cb - Callback function
   */
  destination: function (_req, _file, cb) {
    cb(null, uploadDir);
  },

  /**
   * Generate unique filename for uploaded files
   * @param {Object} _req - Express request object (unused)
   * @param {Object} file - File object
   * @param {Function} cb - Callback function
   */
  filename: function (_req, file, cb) {
    const uniquename = Date.now() + path.extname(file.originalname);
    cb(null, uniquename);
  }
});

/**
 * File filter to allow only image files
 * @param {Object} _req - Express request object (unused)
 * @param {Object} file - File object
 * @param {Function} cb - Callback function
 */
const fileFilter = (_req, file, cb) => {
  const allowedTypes = /jpeg|jpg|png|gif/;
  const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedTypes.test(file.mimetype);

  if (extname && mimetype) {
    cb(null, true);
  } else {
    cb(new Error("Only image files (jpeg, jpg, png, gif) are allowed"));
  }
};

/**
 * Multer upload middleware configuration
 * @type {Object}
 * @property {Object} storage - Storage configuration
 * @property {Function} fileFilter - File filtering function
 * @property {Object} limits - File size limits
 */
const upload = multer({
  storage,
  fileFilter,
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB limit
});

// Export both as default and named export for compatibility
export { upload };
export default upload;
