<!-- Course Creation/Edit Form -->
<div class="course-form-page">
  <div class="page-header">
    <div class="container">
      <div class="header-content">
        <div class="header-text">
          <h1 class="page-title">
            <%= typeof course !== 'undefined' && course ? 'Edit Course' : 'Create New Course' %>
          </h1>
          <p class="page-description">
            <%= typeof course !== 'undefined' && course ? 'Update your course information' : 'Share your knowledge and create an amazing learning experience' %>
          </p>
        </div>
        <div class="header-actions">
          <a href="/instructor/courses" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i>
            <span>Back to Courses</span>
          </a>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content">
    <div class="container">
      <form id="courseForm" class="course-form" enctype="multipart/form-data">
        <div class="form-sections">
          <!-- Basic Information Section -->
          <div class="form-section active" data-section="basic">
            <div class="section-header">
              <h2 class="section-title">
                <i class="fas fa-info-circle"></i>
                <span>Basic Information</span>
              </h2>
              <p class="section-description">Essential details about your course</p>
            </div>
            
            <div class="form-grid">
              <div class="form-group full-width">
                <label for="title" class="form-label required">Course Title</label>
                <input 
                  type="text" 
                  id="title" 
                  name="title" 
                  class="form-input" 
                  placeholder="Enter an engaging course title"
                  value="<%= typeof course !== 'undefined' && course ? course.title : '' %>"
                  required
                  maxlength="100"
                >
                <div class="form-help">
                  <span class="char-count">0/100</span>
                  <span class="help-text">Make it clear and compelling</span>
                </div>
              </div>
              
              <div class="form-group full-width">
                <label for="description" class="form-label required">Course Description</label>
                <textarea 
                  id="description" 
                  name="description" 
                  class="form-textarea" 
                  placeholder="Describe what students will learn and achieve"
                  required
                  maxlength="2000"
                  rows="4"
                ><%= typeof course !== 'undefined' && course ? course.description : '' %></textarea>
                <div class="form-help">
                  <span class="char-count">0/2000</span>
                  <span class="help-text">Explain the value and outcomes</span>
                </div>
              </div>
              
              <div class="form-group">
                <label for="category" class="form-label required">Category</label>
                <select id="category" name="category" class="form-select" required>
                  <option value="">Select a category</option>
                  <option value="Programming" <%= typeof course !== 'undefined' && course && course.category === 'Programming' ? 'selected' : '' %>>Programming</option>
                  <option value="Web Development" <%= typeof course !== 'undefined' && course && course.category === 'Web Development' ? 'selected' : '' %>>Web Development</option>
                  <option value="Mobile Development" <%= typeof course !== 'undefined' && course && course.category === 'Mobile Development' ? 'selected' : '' %>>Mobile Development</option>
                  <option value="Data Science" <%= typeof course !== 'undefined' && course && course.category === 'Data Science' ? 'selected' : '' %>>Data Science</option>
                  <option value="DevOps" <%= typeof course !== 'undefined' && course && course.category === 'DevOps' ? 'selected' : '' %>>DevOps</option>
                  <option value="Design" <%= typeof course !== 'undefined' && course && course.category === 'Design' ? 'selected' : '' %>>Design</option>
                  <option value="Business" <%= typeof course !== 'undefined' && course && course.category === 'Business' ? 'selected' : '' %>>Business</option>
                </select>
              </div>
              
              <div class="form-group">
                <label for="level" class="form-label required">Difficulty Level</label>
                <select id="level" name="level" class="form-select" required>
                  <option value="">Select difficulty</option>
                  <option value="Beginner" <%= typeof course !== 'undefined' && course && course.level === 'Beginner' ? 'selected' : '' %>>Beginner</option>
                  <option value="Intermediate" <%= typeof course !== 'undefined' && course && course.level === 'Intermediate' ? 'selected' : '' %>>Intermediate</option>
                  <option value="Advanced" <%= typeof course !== 'undefined' && course && course.level === 'Advanced' ? 'selected' : '' %>>Advanced</option>
                </select>
              </div>
              
              <div class="form-group">
                <label for="duration" class="form-label required">Duration (hours)</label>
                <input 
                  type="number" 
                  id="duration" 
                  name="duration" 
                  class="form-input" 
                  placeholder="0.5"
                  value="<%= typeof course !== 'undefined' && course ? course.duration : '' %>"
                  min="0.5" 
                  max="500" 
                  step="0.5"
                  required
                >
                <div class="form-help">
                  <span class="help-text">Estimated completion time</span>
                </div>
              </div>
              
              <div class="form-group">
                <label for="price" class="form-label required">Price ($)</label>
                <input 
                  type="number" 
                  id="price" 
                  name="price" 
                  class="form-input" 
                  placeholder="99.99"
                  value="<%= typeof course !== 'undefined' && course ? course.price : '' %>"
                  min="0" 
                  step="0.01"
                  required
                >
                <div class="form-help">
                  <span class="help-text">Set to 0 for free courses</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Course Image Section -->
          <div class="form-section" data-section="image">
            <div class="section-header">
              <h2 class="section-title">
                <i class="fas fa-image"></i>
                <span>Course Image</span>
              </h2>
              <p class="section-description">Upload an attractive course thumbnail</p>
            </div>
            
            <div class="image-upload-container">
              <div class="image-preview" id="imagePreview">
                <% if (typeof course !== 'undefined' && course && course.imageUrl) { %>
                  <img src="<%= course.imageUrl %>" alt="Course image" class="preview-image">
                <% } else { %>
                  <div class="preview-placeholder">
                    <i class="fas fa-image"></i>
                    <span>No image selected</span>
                  </div>
                <% } %>
              </div>
              
              <div class="upload-controls">
                <input type="file" id="courseImage" name="image" accept="image/*" class="file-input">
                <label for="courseImage" class="btn btn-outline">
                  <i class="fas fa-upload"></i>
                  <span>Choose Image</span>
                </label>
                <button type="button" class="btn btn-secondary" onclick="removeImage()">
                  <i class="fas fa-trash"></i>
                  <span>Remove</span>
                </button>
              </div>
              
              <div class="upload-guidelines">
                <h4>Image Guidelines:</h4>
                <ul>
                  <li>Recommended size: 1280x720 pixels</li>
                  <li>Maximum file size: 5MB</li>
                  <li>Supported formats: JPG, PNG, GIF</li>
                  <li>Use high-quality, relevant images</li>
                </ul>
              </div>
            </div>
          </div>
          
          <!-- Additional Details Section -->
          <div class="form-section" data-section="details">
            <div class="section-header">
              <h2 class="section-title">
                <i class="fas fa-list"></i>
                <span>Additional Details</span>
              </h2>
              <p class="section-description">Prerequisites and learning outcomes</p>
            </div>
            
            <div class="form-grid">
              <div class="form-group full-width">
                <label for="tags" class="form-label">Tags</label>
                <input 
                  type="text" 
                  id="tags" 
                  name="tags" 
                  class="form-input" 
                  placeholder="javascript, react, frontend (comma-separated)"
                  value="<%= typeof course !== 'undefined' && course && course.tags ? course.tags.join(', ') : '' %>"
                >
                <div class="form-help">
                  <span class="help-text">Help students find your course</span>
                </div>
              </div>
              
              <div class="form-group full-width">
                <label for="prerequisites" class="form-label">Prerequisites</label>
                <textarea 
                  id="prerequisites" 
                  name="prerequisites" 
                  class="form-textarea" 
                  placeholder="List what students should know before taking this course (one per line)"
                  rows="3"
                ><%= typeof course !== 'undefined' && course && course.prerequisites ? course.prerequisites.join('\n') : '' %></textarea>
                <div class="form-help">
                  <span class="help-text">What should students know beforehand?</span>
                </div>
              </div>
              
              <div class="form-group full-width">
                <label for="learningOutcomes" class="form-label">Learning Outcomes</label>
                <textarea 
                  id="learningOutcomes" 
                  name="learningOutcomes" 
                  class="form-textarea" 
                  placeholder="What will students learn and be able to do? (one per line)"
                  rows="4"
                ><%= typeof course !== 'undefined' && course && course.learningOutcomes ? course.learningOutcomes.join('\n') : '' %></textarea>
                <div class="form-help">
                  <span class="help-text">What will students achieve?</span>
                </div>
              </div>
              
              <div class="form-group">
                <label for="maxEnrollments" class="form-label">Max Enrollments</label>
                <input 
                  type="number" 
                  id="maxEnrollments" 
                  name="maxEnrollments" 
                  class="form-input" 
                  placeholder="100"
                  value="<%= typeof course !== 'undefined' && course ? course.maxEnrollments : '' %>"
                  min="1"
                >
                <div class="form-help">
                  <span class="help-text">Leave empty for unlimited</span>
                </div>
              </div>
              
              <div class="form-group">
                <label for="status" class="form-label">Status</label>
                <select id="status" name="status" class="form-select">
                  <option value="draft" <%= typeof course !== 'undefined' && course && course.status === 'draft' ? 'selected' : '' %>>Draft</option>
                  <option value="published" <%= typeof course !== 'undefined' && course && course.status === 'published' ? 'selected' : '' %>>Published</option>
                </select>
                <div class="form-help">
                  <span class="help-text">Draft courses are not visible to students</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Form Navigation -->
        <div class="form-navigation">
          <div class="nav-steps">
            <button type="button" class="step-btn active" data-step="basic">
              <span class="step-number">1</span>
              <span class="step-label">Basic Info</span>
            </button>
            <button type="button" class="step-btn" data-step="image">
              <span class="step-number">2</span>
              <span class="step-label">Image</span>
            </button>
            <button type="button" class="step-btn" data-step="details">
              <span class="step-number">3</span>
              <span class="step-label">Details</span>
            </button>
          </div>
          
          <div class="nav-actions">
            <button type="button" class="btn btn-outline" id="prevBtn" onclick="previousStep()" disabled>
              <i class="fas fa-arrow-left"></i>
              <span>Previous</span>
            </button>
            <button type="button" class="btn btn-primary" id="nextBtn" onclick="nextStep()">
              <span>Next</span>
              <i class="fas fa-arrow-right"></i>
            </button>
            <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
              <i class="fas fa-save"></i>
              <span><%= typeof course !== 'undefined' && course ? 'Update Course' : 'Create Course' %></span>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
let currentStep = 0;
const steps = ['basic', 'image', 'details'];

// Form navigation
function nextStep() {
  if (currentStep < steps.length - 1) {
    if (validateCurrentStep()) {
      currentStep++;
      showStep(currentStep);
    }
  }
}

function previousStep() {
  if (currentStep > 0) {
    currentStep--;
    showStep(currentStep);
  }
}

function showStep(stepIndex) {
  // Hide all sections
  document.querySelectorAll('.form-section').forEach(section => {
    section.classList.remove('active');
  });
  
  // Show current section
  document.querySelector(`[data-section="${steps[stepIndex]}"]`).classList.add('active');
  
  // Update step buttons
  document.querySelectorAll('.step-btn').forEach((btn, index) => {
    btn.classList.toggle('active', index === stepIndex);
    btn.classList.toggle('completed', index < stepIndex);
  });
  
  // Update navigation buttons
  document.getElementById('prevBtn').disabled = stepIndex === 0;
  document.getElementById('nextBtn').style.display = stepIndex === steps.length - 1 ? 'none' : 'block';
  document.getElementById('submitBtn').style.display = stepIndex === steps.length - 1 ? 'block' : 'none';
}

// Form validation
function validateCurrentStep() {
  const currentSection = document.querySelector(`[data-section="${steps[currentStep]}"]`);
  const requiredFields = currentSection.querySelectorAll('[required]');
  let isValid = true;
  
  requiredFields.forEach(field => {
    if (!field.value.trim()) {
      field.classList.add('error');
      isValid = false;
    } else {
      field.classList.remove('error');
    }
  });
  
  return isValid;
}

// Image handling
document.getElementById('courseImage').addEventListener('change', function(e) {
  const file = e.target.files[0];
  if (file) {
    const reader = new FileReader();
    reader.onload = function(e) {
      document.getElementById('imagePreview').innerHTML = `
        <img src="${e.target.result}" alt="Course image" class="preview-image">
      `;
    };
    reader.readAsDataURL(file);
  }
});

function removeImage() {
  document.getElementById('courseImage').value = '';
  document.getElementById('imagePreview').innerHTML = `
    <div class="preview-placeholder">
      <i class="fas fa-image"></i>
      <span>No image selected</span>
    </div>
  `;
}

// Character counting
function setupCharacterCounting() {
  const fields = [
    { id: 'title', max: 100 },
    { id: 'description', max: 2000 }
  ];
  
  fields.forEach(field => {
    const element = document.getElementById(field.id);
    const counter = element.parentElement.querySelector('.char-count');
    
    element.addEventListener('input', function() {
      const count = this.value.length;
      counter.textContent = `${count}/${field.max}`;
      counter.classList.toggle('warning', count > field.max * 0.9);
      counter.classList.toggle('error', count > field.max);
    });
    
    // Initial count
    element.dispatchEvent(new Event('input'));
  });
}

// Form submission
document.getElementById('courseForm').addEventListener('submit', async function(e) {
  e.preventDefault();
  
  if (!validateCurrentStep()) {
    return;
  }
  
  const formData = new FormData(this);
  const isEdit = <%= typeof course !== 'undefined' && course ? 'true' : 'false' %>;
  const courseId = '<%= typeof course !== 'undefined' && course ? course._id : '' %>';
  
  try {
    const url = isEdit ? `/courses/api/courses/${courseId}` : '/courses/api/courses';
    const method = isEdit ? 'PUT' : 'POST';
    
    const response = await fetch(url, {
      method: method,
      headers: {
        'Authorization': `Bearer ${getAccessToken()}`
      },
      body: formData
    });
    
    const data = await response.json();
    
    if (data.success) {
      showSuccessMessage(isEdit ? 'Course updated successfully!' : 'Course created successfully!');
      setTimeout(() => {
        window.location.href = '/instructor/courses';
      }, 2000);
    } else {
      showErrorMessage(data.message || 'Failed to save course');
    }
  } catch (error) {
    console.error('Error saving course:', error);
    showErrorMessage('Failed to save course');
  }
});

// Utility functions
function getAccessToken() {
  return document.cookie
    .split('; ')
    .find(row => row.startsWith('accessToken='))
    ?.split('=')[1];
}

function showSuccessMessage(message) {
  // Implementation for success message
  alert(message);
}

function showErrorMessage(message) {
  // Implementation for error message
  alert(message);
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
  setupCharacterCounting();
  showStep(0);
  
  // Step button navigation
  document.querySelectorAll('.step-btn').forEach((btn, index) => {
    btn.addEventListener('click', function() {
      if (index <= currentStep || validateStepsUpTo(index - 1)) {
        currentStep = index;
        showStep(currentStep);
      }
    });
  });
});

function validateStepsUpTo(stepIndex) {
  for (let i = 0; i <= stepIndex; i++) {
    const section = document.querySelector(`[data-section="${steps[i]}"]`);
    const requiredFields = section.querySelectorAll('[required]');
    
    for (let field of requiredFields) {
      if (!field.value.trim()) {
        return false;
      }
    }
  }
  return true;
}
</script>
