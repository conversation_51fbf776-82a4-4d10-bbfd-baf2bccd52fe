<!-- Header Component -->
<header class="main-header" role="banner">
    <div class="header-container">
        <!-- Logo/Brand -->
        <div class="header-brand">
            <a href="/" class="brand-link" aria-label="JooCourses Home">
                <img src="/images/logo.png" alt="JooCourses" class="brand-logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <h1 class="brand-text">JooCourses</h1>
            </a>
        </div>

        <!-- Desktop Navigation -->
        <nav class="desktop-nav" id="desktopNav" role="navigation" aria-label="Main navigation">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="/" class="nav-link <%= currentPage === 'home' ? 'active' : '' %>" aria-current="<%= currentPage === 'home' ? 'page' : 'false' %>">
                        <i class="fas fa-home nav-icon"></i>
                        <span>Home</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/courses" class="nav-link <%= currentPage === 'courses' ? 'active' : '' %>" aria-current="<%= currentPage === 'courses' ? 'page' : 'false' %>">
                        <i class="fas fa-book nav-icon"></i>
                        <span>Courses</span>
                    </a>
                </li>

                <% if (typeof user !== 'undefined' && user) { %>
                    <!-- User is logged in -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="userDropdown" role="button" aria-haspopup="true" aria-expanded="false">
                            <% if (user.photo) { %>
                                <img src="<%= user.photo %>" alt="<%= user.name %>" class="user-avatar">
                            <% } else { %>
                                <i class="fas fa-user-circle nav-icon"></i>
                            <% } %>
                            <span><%= user.name %></span>
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="userDropdown">
                            <li class="dropdown-header">
                                <span class="user-name"><%= user.name %></span>
                                <span class="user-role"><%= user.role %></span>
                            </li>
                            <li class="dropdown-divider"></li>
                            <% if (user.role === 'student') { %>
                                <li><a href="/Student_Dashboard" class="dropdown-link"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                                <li><a href="/my-courses" class="dropdown-link"><i class="fas fa-book-open"></i> My Courses</a></li>
                            <% } else if (user.role === 'instructor') { %>
                                <li><a href="/instructor_Dashboard" class="dropdown-link"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                                <li><a href="/instructor/courses" class="dropdown-link"><i class="fas fa-chalkboard-teacher"></i> My Courses</a></li>
                                <li><a href="/instructor/courses/new" class="dropdown-link"><i class="fas fa-plus"></i> Create Course</a></li>
                            <% } else if (user.role === 'manager') { %>
                                <li><a href="/manager_Dashboard" class="dropdown-link"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                                <li><a href="/admin/users" class="dropdown-link"><i class="fas fa-users"></i> Manage Users</a></li>
                                <li><a href="/admin/courses" class="dropdown-link"><i class="fas fa-cogs"></i> Manage Courses</a></li>
                            <% } %>
                            <li class="dropdown-divider"></li>
                            <li><a href="/profile" class="dropdown-link"><i class="fas fa-user-edit"></i> Profile</a></li>
                            <li><a href="/settings" class="dropdown-link"><i class="fas fa-cog"></i> Settings</a></li>
                            <li class="dropdown-divider"></li>
                            <li><a href="#" class="dropdown-link logout-btn" id="logoutBtn"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                <% } else { %>
                    <!-- User is not logged in -->
                    <li class="nav-item">
                        <a href="/login" class="nav-link btn-outline">
                            <i class="fas fa-sign-in-alt nav-icon"></i>
                            <span>Login</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/register" class="nav-link btn-primary">
                            <i class="fas fa-user-plus nav-icon"></i>
                            <span>Register</span>
                        </a>
                    </li>
                <% } %>
            </ul>
        </nav>

        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-toggle" onclick="toggleMobileMenu()" aria-label="Toggle mobile menu" aria-expanded="false">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
        </button>
    </div>
</header>

<!-- Mobile Sidebar -->
<div class="mobile-sidebar" id="mobileSidebar" role="navigation" aria-label="Mobile navigation">
    <div class="mobile-sidebar-header">
        <h2 class="sidebar-title">Menu</h2>
        <button class="close-sidebar" onclick="toggleMobileMenu()" aria-label="Close mobile menu">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <nav class="mobile-nav" id="mobileNav">
        <ul class="mobile-nav-list">
            <li class="mobile-nav-item">
                <a href="/" class="mobile-nav-link" onclick="closeMobileMenu()">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
            </li>
            <li class="mobile-nav-item">
                <a href="/courses" class="mobile-nav-link" onclick="closeMobileMenu()">
                    <i class="fas fa-book"></i>
                    <span>Courses</span>
                </a>
            </li>

            <% if (typeof user !== 'undefined' && user) { %>
                <!-- User is logged in -->
                <li class="mobile-nav-divider"></li>
                <li class="mobile-nav-header">
                    <div class="mobile-user-info">
                        <% if (user.photo) { %>
                            <img src="<%= user.photo %>" alt="<%= user.name %>" class="mobile-user-avatar">
                        <% } else { %>
                            <i class="fas fa-user-circle mobile-user-icon"></i>
                        <% } %>
                        <div class="mobile-user-details">
                            <span class="mobile-user-name"><%= user.name %></span>
                            <span class="mobile-user-role"><%= user.role %></span>
                        </div>
                    </div>
                </li>

                <% if (user.role === 'student') { %>
                    <li class="mobile-nav-item">
                        <a href="/Student_Dashboard" class="mobile-nav-link" onclick="closeMobileMenu()">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="mobile-nav-item">
                        <a href="/my-courses" class="mobile-nav-link" onclick="closeMobileMenu()">
                            <i class="fas fa-book-open"></i>
                            <span>My Courses</span>
                        </a>
                    </li>
                <% } else if (user.role === 'instructor') { %>
                    <li class="mobile-nav-item">
                        <a href="/instructor_Dashboard" class="mobile-nav-link" onclick="closeMobileMenu()">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="mobile-nav-item">
                        <a href="/instructor/courses" class="mobile-nav-link" onclick="closeMobileMenu()">
                            <i class="fas fa-chalkboard-teacher"></i>
                            <span>My Courses</span>
                        </a>
                    </li>
                    <li class="mobile-nav-item">
                        <a href="/instructor/courses/new" class="mobile-nav-link" onclick="closeMobileMenu()">
                            <i class="fas fa-plus"></i>
                            <span>Create Course</span>
                        </a>
                    </li>
                <% } else if (user.role === 'manager') { %>
                    <li class="mobile-nav-item">
                        <a href="/manager_Dashboard" class="mobile-nav-link" onclick="closeMobileMenu()">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="mobile-nav-item">
                        <a href="/admin/users" class="mobile-nav-link" onclick="closeMobileMenu()">
                            <i class="fas fa-users"></i>
                            <span>Manage Users</span>
                        </a>
                    </li>
                    <li class="mobile-nav-item">
                        <a href="/admin/courses" class="mobile-nav-link" onclick="closeMobileMenu()">
                            <i class="fas fa-cogs"></i>
                            <span>Manage Courses</span>
                        </a>
                    </li>
                <% } %>

                <li class="mobile-nav-divider"></li>
                <li class="mobile-nav-item">
                    <a href="/profile" class="mobile-nav-link" onclick="closeMobileMenu()">
                        <i class="fas fa-user-edit"></i>
                        <span>Profile</span>
                    </a>
                </li>
                <li class="mobile-nav-item">
                    <a href="/settings" class="mobile-nav-link" onclick="closeMobileMenu()">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
                <li class="mobile-nav-item">
                    <a href="#" class="mobile-nav-link logout-btn" id="logoutBtnMobile" onclick="closeMobileMenu()">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </li>
            <% } else { %>
                <!-- User is not logged in -->
                <li class="mobile-nav-divider"></li>
                <li class="mobile-nav-item">
                    <a href="/login" class="mobile-nav-link" onclick="closeMobileMenu()">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>Login</span>
                    </a>
                </li>
                <li class="mobile-nav-item">
                    <a href="/register" class="mobile-nav-link btn-primary" onclick="closeMobileMenu()">
                        <i class="fas fa-user-plus"></i>
                        <span>Register</span>
                    </a>
                </li>
            <% } %>
        </ul>
    </nav>
</div>

<!-- Mobile Overlay -->
<div class="mobile-overlay" id="overlay" onclick="toggleMobileMenu()"></div>
<!-- Header JavaScript -->
<script>
/**
 * Mobile menu functionality
 */
function toggleMobileMenu() {
    const sidebar = document.getElementById('mobileSidebar');
    const overlay = document.getElementById('overlay');
    const toggle = document.querySelector('.mobile-menu-toggle');

    const isActive = sidebar.classList.contains('active');

    if (isActive) {
        closeMobileMenu();
    } else {
        sidebar.classList.add('active');
        overlay.classList.add('active');
        toggle.classList.add('active');
        toggle.setAttribute('aria-expanded', 'true');

        // Focus management for accessibility
        const firstLink = sidebar.querySelector('.mobile-nav-link');
        if (firstLink) firstLink.focus();
    }
}

function closeMobileMenu() {
    const sidebar = document.getElementById('mobileSidebar');
    const overlay = document.getElementById('overlay');
    const toggle = document.querySelector('.mobile-menu-toggle');

    sidebar.classList.remove('active');
    overlay.classList.remove('active');
    toggle.classList.remove('active');
    toggle.setAttribute('aria-expanded', 'false');
}

/**
 * Dropdown functionality for desktop
 */
function initDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown');

    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');

        if (!toggle || !menu) return;

        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Close other dropdowns
            dropdowns.forEach(otherDropdown => {
                if (otherDropdown !== dropdown) {
                    otherDropdown.classList.remove('active');
                    const otherToggle = otherDropdown.querySelector('.dropdown-toggle');
                    if (otherToggle) otherToggle.setAttribute('aria-expanded', 'false');
                }
            });

            // Toggle current dropdown
            const isActive = dropdown.classList.contains('active');
            dropdown.classList.toggle('active');
            toggle.setAttribute('aria-expanded', !isActive);
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function() {
        dropdowns.forEach(dropdown => {
            dropdown.classList.remove('active');
            const toggle = dropdown.querySelector('.dropdown-toggle');
            if (toggle) toggle.setAttribute('aria-expanded', 'false');
        });
    });
}

/**
 * Logout functionality
 */
async function handleLogout(e) {
    e.preventDefault();

    try {
        // Show loading state
        const button = e.target.closest('a');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging out...';
        button.style.pointerEvents = 'none';

        // Call backend logout endpoint
        await fetch('/api/logout', {
            method: 'POST',
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json'
            }
        });
    } catch (err) {
        console.warn('Logout API call failed:', err);
        // Continue with client-side cleanup even if API fails
    }

    // Clear client-side data
    document.cookie = "accessToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; samesite=strict";
    document.cookie = "refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; secure; samesite=strict";
    sessionStorage.removeItem('user');
    localStorage.removeItem('user');

    // Redirect to login page
    window.location.href = '/login';
}

/**
 * Initialize header functionality
 */
function initHeader() {
    // Initialize dropdowns
    initDropdowns();

    // Attach logout handlers
    const logoutButtons = document.querySelectorAll('.logout-btn');
    logoutButtons.forEach(button => {
        button.addEventListener('click', handleLogout);
    });

    // Mobile menu event listeners
    document.addEventListener('click', function(event) {
        const sidebar = document.getElementById('mobileSidebar');
        const toggle = document.querySelector('.mobile-menu-toggle');

        if (!sidebar.contains(event.target) && !toggle.contains(event.target) && sidebar.classList.contains('active')) {
            closeMobileMenu();
        }
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeMobileMenu();

            // Close dropdowns
            const dropdowns = document.querySelectorAll('.dropdown.active');
            dropdowns.forEach(dropdown => {
                dropdown.classList.remove('active');
                const toggle = dropdown.querySelector('.dropdown-toggle');
                if (toggle) toggle.setAttribute('aria-expanded', 'false');
            });
        }
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initHeader);

</script>