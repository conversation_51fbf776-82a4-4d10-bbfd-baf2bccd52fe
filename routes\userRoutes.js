/**
 * @fileoverview User routes - API endpoints for user operations
 * @module routes/userRoutes
 */

import express from 'express';
import { authenticate, authorize } from '../middlewares/auth.js';
import { upload } from '../middlewares/upload.js';
import {
  addUser,
  showAllUsers,
  getUserById,
  updateUser,
  changePassword,
  deleteUser,
  profile,
  updateImg
} from '../controllers/userController.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - name
 *         - email
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *         email:
 *           type: string
 *           format: email
 *         role:
 *           type: string
 *           enum: [student, instructor, manager]
 *         photo:
 *           type: string
 *         age:
 *           type: number
 *           minimum: 0
 *           maximum: 120
 *         phone:
 *           type: string
 */

// ===== Public User Routes =====

/**
 * Create a new user (admin only)
 * @route POST /api/users
 * @access Private (Manager only)
 * @body {Object} userData - User data
 * @file {File} [photo] - User profile photo
 */
router.post('/api/users', 
  authenticate, 
  authorize('manager'), 
  upload.single('photo'), 
  addUser
);

/**
 * Get all users with pagination and filtering
 * @route GET /api/users
 * @access Private (Manager only)
 * @param {number} [page=1] - Page number
 * @param {number} [limit=10] - Items per page
 * @param {string} [role] - Filter by role
 * @param {string} [search] - Search term
 */
router.get('/api/users', 
  authenticate, 
  authorize('manager'), 
  showAllUsers
);

/**
 * Get user by ID
 * @route GET /api/users/:id
 * @access Private (Manager only or own profile)
 * @param {string} id - User ID
 */
router.get('/api/users/:id', 
  authenticate, 
  getUserById
);

/**
 * Update user by ID
 * @route PUT /api/users/:id
 * @access Private (Manager only or own profile)
 * @param {string} id - User ID
 * @body {Object} updateData - User update data
 */
router.put('/api/users/:id', 
  authenticate, 
  updateUser
);

/**
 * Delete user by ID
 * @route DELETE /api/users/:id
 * @access Private (Manager only)
 * @param {string} id - User ID
 */
router.delete('/api/users/:id', 
  authenticate, 
  authorize('manager'), 
  deleteUser
);

// ===== User Profile Routes =====

/**
 * Get current user profile
 * @route GET /api/users/profile
 * @access Private
 * @header {string} Authorization - Bearer token
 */
router.get('/api/users/profile', 
  authenticate, 
  profile
);

/**
 * Update current user profile photo
 * @route PUT /api/users/profile/photo
 * @access Private
 * @header {string} Authorization - Bearer token
 * @file {File} photo - New profile photo
 */
router.put('/api/users/profile/photo', 
  authenticate, 
  upload.single('photo'), 
  updateImg
);

/**
 * Change current user password
 * @route PUT /api/users/profile/password
 * @access Private
 * @header {string} Authorization - Bearer token
 * @body {Object} passwordData - Password change data
 * @body {string} passwordData.currentPassword - Current password
 * @body {string} passwordData.newPassword - New password
 * @body {string} passwordData.confirmPassword - Password confirmation
 */
router.put('/api/users/profile/password', 
  authenticate, 
  changePassword
);

// ===== Legacy Routes (for backward compatibility) =====

/**
 * @deprecated Use POST /api/users instead
 */
router.post('/api/addUser', 
  upload.single('photo'), 
  addUser
);

/**
 * @deprecated Use GET /api/users/profile instead
 */
router.get('/api/users/me', 
  authenticate, 
  profile
);

/**
 * @deprecated Use PUT /api/users/profile/photo instead
 */
router.patch('/api/users/updateimg/:id', 
  authenticate,
  upload.single('photo'), 
  updateImg
);

/**
 * @deprecated Use PUT /api/users/:id instead
 */
router.put('/api/updateUser/:id', 
  authenticate,
  updateUser
);

/**
 * @deprecated Use PUT /api/users/profile/password instead
 */
router.patch('/api/changePassword/:id', 
  authenticate,
  changePassword
);

/**
 * @deprecated Use DELETE /api/users/:id instead
 */
router.delete('/api/deleteUser/:id', 
  authenticate,
  authorize('manager'),
  deleteUser
);

export default router;
