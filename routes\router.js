import express from "express";
import { verifyRefreshToken, authenticateWeb, authorizeWeb } from '../middlewares/auth.js';
import upload from "../middlewares/upload.js";
import passport from '../config/passport.js';
import login from '../controllers/auth/login.js';
import register from '../controllers/auth/register.js';
import { addUser  , showAllUsers ,getUserById,updateUser , changePassword ,deleteUser, profile , updateImg } from '../controllers/userController.js';
const router = express.Router();
import {authenticate} from '../middlewares/auth.js';
import multer from "multer";
import logout from '../controllers/auth/logout.js';
import * as courseService from '../Services/courseService.js';


router.post('/api/addUser' , upload.single("photo") ,   addUser); 
router.get('/api/users',  showAllUsers); 
router.get('/api/users/me',authenticate,  profile); 

router.patch('/api/users/updateimg/:id', (req, res, next) => {
  upload.single('photo')(req, res, function(err) {
    if (err instanceof multer.MulterError) {
      return res.status(400).json({ message: err.message });
    } else if (err) {
      return res.status(400).json({ message: err.message });
    }
    next();
  });
}, updateImg);


router.put('/api/updateUser/:id',  updateUser); 
router.patch('/api/changePassword/:id',  changePassword); 

router.delete('/api/deleteUser/:id' , deleteUser);

router.get('/refresh-token', verifyRefreshToken, (req, res) => {
  const accessToken = jwt.sign(
      { id: req.user.id },  
      process.env.JWT_SECRET,
      { expiresIn: '15m' }
  );

  res.json({ accessToken });
});
// Auth 
router.post('/api/register', register); 
router.post('/api/login' , login);

// Add logout route
router.post('/api/logout', logout);

// ✅ Google OAuth routes

// Step 1: Redirect user to Google for authentication
router.get('/auth/google',
    passport.authenticate('google', { scope: ['profile', 'email'] })
  );
  
  // Step 2: Google redirects back to this route after login
  router.get('/auth/google/callback',
    passport.authenticate('google', { failureRedirect: '/login' }),
    (req, res) => {
      // 🔁 بعد تسجيل الدخول بنجاح، المستخدم يروح لصفحة معينة
      res.redirect('http://127.0.0.1:5500/index.html'); // عدّل حسب ما تحب
    }
  );
  
  // Optional: Logout route
  router.get('/auth/logout', (req, res) => {
    req.logout(() => {
      res.redirect('/');
    });
  });


// ===== INSTRUCTOR DASHBOARD ROUTES =====

// Instructor Dashboard - Main page with stats and overview
router.get('/instructor_Dashboard', authenticateWeb, authorizeWeb('instructor'), async (req, res) => {
  try {
    const stats = await courseService.getInstructorStats(req.user.userId);
    const recentCourses = await courseService.getRecentCourses(req.user.userId, 5);

    res.render('pages/instructor_Dashboard', {
      title: 'Instructor Dashboard | JooCourses',
      stats,
      recentCourses,
      user: req.user
    });
  } catch (error) {
    console.error('Instructor dashboard error:', error);
    res.status(500).render('pages/error', {
      title: 'Error | JooCourses',
      message: 'Failed to load dashboard data'
    });
  }
});

// Instructor Courses - List all courses with filters
router.get('/instructor/courses', authenticateWeb, authorizeWeb('instructor'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filters = {
      status: req.query.status,
      category: req.query.category,
      level: req.query.level,
      search: req.query.search
    };

    const result = await courseService.getCoursesByInstructor(req.user.userId, filters, page, limit);
    const categories = await courseService.getInstructorCategories(req.user.userId);

    res.render('pages/instructor-courses', {
      title: 'My Courses | JooCourses',
      courses: result.courses,
      pagination: {
        current: result.page,
        total: result.totalPages,
        hasNext: result.page < result.totalPages,
        hasPrev: result.page > 1
      },
      filters,
      categories,
      user: req.user,
      success: req.query.success,
      error: req.query.error
    });
  } catch (error) {
    console.error('Instructor courses error:', error);
    res.status(500).render('pages/error', {
      title: 'Error | JooCourses',
      message: 'Failed to load courses'
    });
  }
});

// Course Form - Create new course
router.get('/instructor/courses/new', authenticateWeb, authorizeWeb('instructor'), (req, res) => {
  res.render('pages/course-form', {
    title: 'Create New Course | JooCourses',
    course: null,
    isEdit: false,
    user: req.user
  });
});

// Course Form - Edit existing course
router.get('/instructor/courses/:id/edit', authenticateWeb, authorizeWeb('instructor'), async (req, res) => {
  try {
    await courseService.validateCourseOwnership(req.params.id, req.user.userId);
    const course = await courseService.getCourseById(req.params.id);

    if (!course) {
      return res.status(404).render('pages/404', {
        title: 'Course Not Found | JooCourses'
      });
    }

    res.render('pages/course-form', {
      title: 'Edit Course | JooCourses',
      course,
      isEdit: true,
      user: req.user
    });
  } catch (error) {
    console.error('Course edit error:', error);
    if (error.message.includes('permission')) {
      return res.status(403).render('pages/403', {
        title: 'Access Denied | JooCourses',
        message: error.message
      });
    }
    res.status(500).render('pages/error', {
      title: 'Error | JooCourses',
      message: 'Failed to load course for editing'
    });
  }
});

// Create Course - POST
router.post('/instructor/courses', authenticateWeb, authorizeWeb('instructor'), upload.single('image'), async (req, res) => {
  try {
    const courseData = { ...req.body };
    courseData.instructor = req.user.userId;

    if (req.file) {
      courseData.imageUrl = `/uploads/${req.file.filename}`;
    }

    await courseService.createCourse(courseData);

    res.redirect(`/instructor/courses?success=Course created successfully`);
  } catch (error) {
    console.error('Course creation error:', error);
    res.render('pages/course-form', {
      title: 'Create New Course | JooCourses',
      course: req.body,
      isEdit: false,
      error: error.message,
      user: req.user
    });
  }
});

// Update Course - PUT
router.put('/instructor/courses/:id', authenticateWeb, authorizeWeb('instructor'), upload.single('image'), async (req, res) => {
  try {
    await courseService.validateCourseOwnership(req.params.id, req.user.userId);

    const updateData = { ...req.body };
    if (req.file) {
      updateData.imageUrl = `/uploads/${req.file.filename}`;
    }

    await courseService.updateCourse(req.params.id, updateData);

    res.redirect(`/instructor/courses?success=Course updated successfully`);
  } catch (error) {
    console.error('Course update error:', error);
    const course = await courseService.getCourseById(req.params.id);
    res.render('pages/course-form', {
      title: 'Edit Course | JooCourses',
      course: { ...course, ...req.body },
      isEdit: true,
      error: error.message,
      user: req.user
    });
  }
});

// Delete Course - DELETE
router.delete('/instructor/courses/:id', authenticateWeb, authorizeWeb('instructor'), async (req, res) => {
  try {
    await courseService.validateCourseOwnership(req.params.id, req.user.userId);
    await courseService.deleteCourse(req.params.id);

    res.json({ success: true, message: 'Course deleted successfully' });
  } catch (error) {
    console.error('Course deletion error:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// ===== PUBLIC VIEW ROUTES =====

router.get('/', (req, res) => {
  res.render('index', { title: 'Home | JooCourses' });
});

router.get('/courses', (req, res) => {
  res.render('pages/courses', { title: 'courses | JooCourses' });
});

router.get('/login', (req, res) => {
  const message = req.query.message;
  res.render('pages/login', {
    title: 'Login | JooCourses',
    message
  });
});

router.get('/register', (req, res) => {
  res.render('pages/register', { title: 'Register | JooCourses' });
});

router.get('/manager_Dashboard', (req, res) => {
  res.render('pages/manager_Dashboard', { title: 'Manager Dashboard | JooCourses' });
});

router.get('/Student_Dashboard', (req, res) => {
  res.render('pages/Student_Dashboard', { title: 'Student Dashboard | JooCourses' });
});

router.get('/404', (req, res) => {
  res.render('pages/404', { title: 'Student Dashboard | JooCourses' });
});
export default router;