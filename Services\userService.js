/**
 * @fileoverview User service layer - handles all user-related business logic
 * @module services/userService
 */

import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import path from 'path';
import User from '../models/usermodel.js';
import { 
  ValidationError, 
  NotFoundError, 
  AuthenticationError,
  DatabaseError 
} from '../utils/errors.js';
import { 
  validateUserData, 
  validatePassword, 
  isValidObjectId,
  validateRequiredFields 
} from '../utils/validation.js';
import { 
  calculatePagination, 
  sanitizeUser 
} from '../utils/helpers.js';
import { USER_ROLES } from '../utils/constants.js';

/**
 * Register a new user
 * @async
 * @function registerUser
 * @param {Object} userData - User registration data
 * @param {string} userData.name - User's full name
 * @param {string} userData.email - User's email address
 * @param {string} userData.password - User's password
 * @param {string} [userData.role=student] - User's role
 * @param {string} [userData.photo] - User's profile photo path
 * @returns {Promise<Object>} Created user object
 * @throws {ValidationError} When user data is invalid
 * @throws {DatabaseError} When user already exists
 * 
 * @example
 * const user = await registerUser({
 *   name: 'John Doe',
 *   email: '<EMAIL>',
 *   password: 'securePassword123',
 *   role: 'student'
 * });
 */
export const registerUser = async (userData) => {
  try {
    // Validate user data
    validateUserData(userData);

    // Check if user already exists
    const existingUser = await User.findOne({ email: userData.email });
    if (existingUser) {
      throw new DatabaseError('User with this email already exists');
    }

    // Validate password strength
    const passwordValidation = validatePassword(userData.password);
    if (!passwordValidation.isValid) {
      throw new ValidationError('Password validation failed', passwordValidation.errors);
    }

    // Create user with hashed password
    const hashedPassword = await bcrypt.hash(userData.password, 10);
    
    const newUser = new User({
      name: userData.name,
      email: userData.email,
      password: hashedPassword,
      role: userData.role || USER_ROLES.STUDENT,
      photo: userData.photo || ''
    });

    const savedUser = await newUser.save();
    
    // Return sanitized user data
    return sanitizeUser(savedUser);
  } catch (error) {
    if (error.code === 11000) {
      throw new DatabaseError('User with this email already exists');
    }
    throw error;
  }
};

/**
 * Authenticate user login
 * @async
 * @function loginUser
 * @param {Object} credentials - Login credentials
 * @param {string} credentials.email - User's email
 * @param {string} credentials.password - User's password
 * @returns {Promise<Object>} Authentication result with tokens
 * @throws {ValidationError} When credentials are invalid
 * @throws {AuthenticationError} When authentication fails
 * 
 * @example
 * const result = await loginUser({
 *   email: '<EMAIL>',
 *   password: 'securePassword123'
 * });
 */
export const loginUser = async (credentials) => {
  validateRequiredFields(credentials, ['email', 'password']);

  // Find user with password field
  const user = await User.findOne({ email: credentials.email }).select('+password');
  if (!user) {
    throw new AuthenticationError('Invalid email or password');
  }

  // Verify password
  const isPasswordValid = await user.isPasswordValid(credentials.password);
  if (!isPasswordValid) {
    throw new AuthenticationError('Invalid email or password');
  }

  // Generate tokens
  const accessToken = jwt.sign(
    { 
      userId: user._id,
      _id: user._id,
      role: user.role,
      email: user.email 
    },
    process.env.JWT_SECRET,
    { expiresIn: '15m' }
  );

  const refreshToken = jwt.sign(
    { userId: user._id },
    process.env.REFRESH_TOKEN_SECRET,
    { expiresIn: '7d' }
  );

  // Update last login
  user.lastLogin = new Date();
  await user.save();

  return {
    user: sanitizeUser(user),
    accessToken,
    refreshToken
  };
};

/**
 * Get all users with pagination and filtering
 * @async
 * @function getAllUsers
 * @param {Object} [options={}] - Query options
 * @param {number} [options.page=1] - Page number
 * @param {number} [options.limit=10] - Items per page
 * @param {string} [options.role] - Filter by role
 * @param {string} [options.search] - Search term
 * @returns {Promise<Object>} Paginated users list
 * 
 * @example
 * const result = await getAllUsers({ 
 *   page: 1, 
 *   limit: 10, 
 *   role: 'instructor' 
 * });
 */
export const getAllUsers = async (options = {}) => {
  const { page = 1, limit = 10, role, search } = options;
  const pagination = calculatePagination(0, page, limit);

  // Build query
  const query = {};
  if (role) query.role = role;
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } }
    ];
  }

  // Execute query with pagination
  const [users, total] = await Promise.all([
    User.find(query)
      .select('-password')
      .skip(pagination.skip)
      .limit(pagination.limit)
      .sort({ createdAt: -1 })
      .lean(),
    User.countDocuments(query)
  ]);

  // Calculate final pagination
  const finalPagination = calculatePagination(total, page, limit);

  return {
    users: users.map(sanitizeUser),
    pagination: finalPagination
  };
};

/**
 * Update user information
 * @async
 * @function updateUser
 * @param {string} userId - User ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} Updated user object
 * @throws {ValidationError} When user data is invalid
 * @throws {NotFoundError} When user is not found
 * 
 * @example
 * const updatedUser = await updateUser('60d5ecb54b24a1234567890a', {
 *   name: 'Jane Doe',
 *   age: 25
 * });
 */
export const updateUser = async (userId, updateData) => {
  if (!isValidObjectId(userId)) {
    throw new ValidationError('Invalid user ID format');
  }

  // Validate update data
  if (Object.keys(updateData).length > 0) {
    validateUserData(updateData);
  }

  // Remove sensitive fields that shouldn't be updated directly
  const { password, role, ...safeUpdateData } = updateData;

  const updatedUser = await User.findByIdAndUpdate(
    userId,
    safeUpdateData,
    { new: true, runValidators: true }
  ).select('-password');

  if (!updatedUser) {
    throw new NotFoundError('User not found');
  }

  return sanitizeUser(updatedUser);
};

/**
 * Get user by ID
 * @async
 * @function getUserById
 * @param {string} userId - User ID
 * @param {boolean} [includePrivate=false] - Include private fields
 * @returns {Promise<Object>} User object
 * @throws {ValidationError} When user ID is invalid
 * @throws {NotFoundError} When user is not found
 * 
 * @example
 * const user = await getUserById('60d5ecb54b24a1234567890a');
 */
export const getUserById = async (userId, includePrivate = false) => {
  if (!isValidObjectId(userId)) {
    throw new ValidationError('Invalid user ID format');
  }

  const selectFields = includePrivate 
    ? '_id name email photo role age phone createdAt lastLogin'
    : '_id name email photo role createdAt';

  const user = await User.findById(userId).select(selectFields).lean();
  
  if (!user) {
    throw new NotFoundError('User not found');
  }

  return sanitizeUser(user);
};

/**
 * Delete user by ID
 * @async
 * @function deleteUser
 * @param {string} userId - User ID
 * @returns {Promise<void>}
 * @throws {ValidationError} When user ID is invalid
 * @throws {NotFoundError} When user is not found
 * 
 * @example
 * await deleteUser('60d5ecb54b24a1234567890a');
 */
export const deleteUser = async (userId) => {
  if (!isValidObjectId(userId)) {
    throw new ValidationError('Invalid user ID format');
  }

  const user = await User.findById(userId);
  if (!user) {
    throw new NotFoundError('User not found');
  }

  await User.findByIdAndDelete(userId);
};

/**
 * Change user password
 * @async
 * @function changePassword
 * @param {string} userId - User ID
 * @param {Object} passwordData - Password change data
 * @param {string} passwordData.currentPassword - Current password
 * @param {string} passwordData.newPassword - New password
 * @param {string} passwordData.confirmPassword - Password confirmation
 * @returns {Promise<void>}
 * @throws {ValidationError} When password data is invalid
 * @throws {NotFoundError} When user is not found
 * @throws {AuthenticationError} When current password is incorrect
 * 
 * @example
 * await changePassword('60d5ecb54b24a1234567890a', {
 *   currentPassword: 'oldPassword123',
 *   newPassword: 'newPassword456',
 *   confirmPassword: 'newPassword456'
 * });
 */
export const changePassword = async (userId, passwordData) => {
  if (!isValidObjectId(userId)) {
    throw new ValidationError('Invalid user ID format');
  }

  validateRequiredFields(passwordData, ['currentPassword', 'newPassword', 'confirmPassword']);

  const { currentPassword, newPassword, confirmPassword } = passwordData;

  // Validate new password
  const passwordValidation = validatePassword(newPassword);
  if (!passwordValidation.isValid) {
    throw new ValidationError('New password validation failed', passwordValidation.errors);
  }

  // Check password confirmation
  if (newPassword !== confirmPassword) {
    throw new ValidationError('New password and confirmation do not match');
  }

  // Get user with password
  const user = await User.findById(userId).select('+password');
  if (!user) {
    throw new NotFoundError('User not found');
  }

  // Verify current password
  const isCurrentPasswordValid = await user.isPasswordValid(currentPassword);
  if (!isCurrentPasswordValid) {
    throw new AuthenticationError('Current password is incorrect');
  }

  // Update password (will be hashed by pre-save middleware)
  user.password = newPassword;
  await user.save();
};

/**
 * Update user profile photo
 * @async
 * @function updateUserPhoto
 * @param {string} userId - User ID
 * @param {string} photoPath - New photo file path
 * @returns {Promise<Object>} Updated user object with photo URL
 * @throws {ValidationError} When user ID is invalid
 * @throws {NotFoundError} When user is not found
 * 
 * @example
 * const user = await updateUserPhoto('60d5ecb54b24a1234567890a', '/uploads/photo.jpg');
 */
export const updateUserPhoto = async (userId, photoPath) => {
  if (!isValidObjectId(userId)) {
    throw new ValidationError('Invalid user ID format');
  }

  const user = await User.findById(userId);
  if (!user) {
    throw new NotFoundError('User not found');
  }

  // Update photo path
  user.photo = photoPath;
  await user.save();

  // Return user with formatted photo URL
  const updatedUser = sanitizeUser(user);
  updatedUser.photo = `/uploads/${path.basename(photoPath)}`;
  
  return updatedUser;
};
