/**
 * @fileoverview Authentication routes - API endpoints for user authentication
 * @module routes/authRoutes
 */

import express from 'express';
import passport from '../config/passport.js';
import { verifyRefreshToken } from '../middlewares/auth.js';
import login from '../controllers/auth/login.js';
import register from '../controllers/auth/register.js';
import logout from '../controllers/auth/logout.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     LoginRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *         password:
 *           type: string
 *           minLength: 6
 *     RegisterRequest:
 *       type: object
 *       required:
 *         - name
 *         - email
 *         - password
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *         email:
 *           type: string
 *           format: email
 *         password:
 *           type: string
 *           minLength: 6
 *         role:
 *           type: string
 *           enum: [student, instructor, manager]
 *           default: student
 */

// ===== Authentication Routes =====

/**
 * Register a new user
 * @route POST /api/register
 * @access Public
 * @body {Object} userData - User registration data
 * @body {string} userData.name - User's full name
 * @body {string} userData.email - User's email address
 * @body {string} userData.password - User's password
 * @body {string} [userData.role=student] - User's role
 */
router.post('/api/register', register);

/**
 * Login user
 * @route POST /api/login
 * @access Public
 * @body {Object} credentials - User login credentials
 * @body {string} credentials.email - User's email address
 * @body {string} credentials.password - User's password
 */
router.post('/api/login', login);

/**
 * Logout user
 * @route POST /api/logout
 * @access Private
 * @header {string} Authorization - Bearer token
 */
router.post('/api/logout', logout);

/**
 * Refresh access token
 * @route POST /api/refresh-token
 * @access Private
 * @header {string} Authorization - Bearer refresh token
 */
router.post('/api/refresh-token', verifyRefreshToken, (req, res) => {
  const accessToken = jwt.sign(
    { 
      userId: req.user.userId,
      _id: req.user._id,
      role: req.user.role,
      email: req.user.email 
    },
    process.env.JWT_SECRET,
    { expiresIn: '15m' }
  );

  res.json({ 
    success: true,
    message: 'Token refreshed successfully',
    data: { accessToken }
  });
});

// ===== OAuth Routes =====

/**
 * Google OAuth login
 * @route GET /auth/google
 * @access Public
 */
router.get('/auth/google', 
  passport.authenticate('google', { scope: ['profile', 'email'] })
);

/**
 * Google OAuth callback
 * @route GET /auth/google/callback
 * @access Public
 */
router.get('/auth/google/callback',
  passport.authenticate('google', { failureRedirect: '/login' }),
  (req, res) => {
    // Successful authentication, redirect to dashboard
    res.redirect('/dashboard');
  }
);

export default router;
