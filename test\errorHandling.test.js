/**
 * @fileoverview Error handling tests
 * @module test/errorHandling
 */

import { 
  ValidationError, 
  NotFoundError, 
  AuthenticationError, 
  AuthorizationError,
  DatabaseError,
  globalErrorHandler 
} from '../utils/errors.js';

/**
 * Test error handling functionality
 * @function testErrorHandling
 * @description Manual test function to verify error handling works correctly
 */
export const testErrorHandling = () => {
  console.log('🧪 Testing Error Handling...\n');

  // Test ValidationError
  try {
    throw new ValidationError('Invalid email format', ['Email must be valid']);
  } catch (error) {
    console.log('✅ ValidationError:', {
      name: error.name,
      message: error.message,
      status: error.status,
      details: error.details
    });
  }

  // Test NotFoundError
  try {
    throw new NotFoundError('User not found');
  } catch (error) {
    console.log('✅ NotFoundError:', {
      name: error.name,
      message: error.message,
      status: error.status
    });
  }

  // Test AuthenticationError
  try {
    throw new AuthenticationError('Invalid credentials');
  } catch (error) {
    console.log('✅ AuthenticationError:', {
      name: error.name,
      message: error.message,
      status: error.status
    });
  }

  // Test AuthorizationError
  try {
    throw new AuthorizationError('Insufficient permissions');
  } catch (error) {
    console.log('✅ AuthorizationError:', {
      name: error.name,
      message: error.message,
      status: error.status
    });
  }

  // Test DatabaseError
  try {
    throw new DatabaseError('Connection failed');
  } catch (error) {
    console.log('✅ DatabaseError:', {
      name: error.name,
      message: error.message,
      status: error.status
    });
  }

  console.log('\n🎉 All error types tested successfully!');
};

/**
 * Test global error handler
 * @function testGlobalErrorHandler
 * @description Test the global error handler middleware
 */
export const testGlobalErrorHandler = () => {
  console.log('🧪 Testing Global Error Handler...\n');

  // Mock request and response objects
  const mockReq = {
    method: 'GET',
    path: '/api/test',
    ip: '127.0.0.1',
    headers: { 'user-agent': 'test-agent' }
  };

  const mockRes = {
    status: function(code) {
      this.statusCode = code;
      return this;
    },
    json: function(data) {
      console.log('📤 Response:', {
        status: this.statusCode,
        body: data
      });
      return this;
    },
    render: function(template, data) {
      console.log('📄 Rendered:', {
        template,
        data
      });
      return this;
    }
  };

  const mockNext = () => {};

  // Test with ValidationError
  console.log('Testing ValidationError handling:');
  const validationError = new ValidationError('Test validation error');
  globalErrorHandler(validationError, mockReq, mockRes, mockNext);

  // Test with generic Error
  console.log('\nTesting generic Error handling:');
  const genericError = new Error('Test generic error');
  globalErrorHandler(genericError, mockReq, mockRes, mockNext);

  console.log('\n🎉 Global error handler tested successfully!');
};

/**
 * Run all error handling tests
 * @function runErrorTests
 * @description Execute all error handling tests
 */
export const runErrorTests = () => {
  console.log('🚀 Starting Error Handling Tests\n');
  console.log('='.repeat(50));
  
  testErrorHandling();
  console.log('\n' + '='.repeat(50));
  testGlobalErrorHandler();
  
  console.log('\n' + '='.repeat(50));
  console.log('✅ All error handling tests completed!');
};

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runErrorTests();
}
