// Instructor Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

function initializeDashboard() {
    // Initialize tooltips and interactive elements
    initializeTooltips();
    
    // Initialize chart animations if needed
    animateStatCards();
    
    // Initialize quick actions
    initializeQuickActions();
    
    // Auto-refresh stats every 5 minutes
    setInterval(refreshStats, 5 * 60 * 1000);
}

function initializeTooltips() {
    // Add tooltips to stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.stat-icon');
            if (icon) {
                icon.style.transform = 'scale(1.1)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.stat-icon');
            if (icon) {
                icon.style.transform = 'scale(1)';
            }
        });
    });
}

function animateStatCards() {
    // Animate stat numbers on page load
    const statNumbers = document.querySelectorAll('.stat-content h3');
    
    statNumbers.forEach(stat => {
        const finalValue = stat.textContent;
        const isNumber = !isNaN(parseFloat(finalValue.replace(/[^0-9.-]/g, '')));
        
        if (isNumber) {
            const numericValue = parseFloat(finalValue.replace(/[^0-9.-]/g, ''));
            const prefix = finalValue.replace(/[0-9.-]/g, '').replace(/\s/g, '');
            
            animateNumber(stat, 0, numericValue, 1500, prefix);
        }
    });
}

function animateNumber(element, start, end, duration, prefix = '') {
    const startTime = performance.now();
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = start + (end - start) * easeOutQuart;
        
        if (prefix.includes('$')) {
            element.textContent = '$' + Math.floor(current).toLocaleString();
        } else if (prefix.includes('.')) {
            element.textContent = current.toFixed(1);
        } else {
            element.textContent = Math.floor(current).toLocaleString();
        }
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        } else {
            element.textContent = prefix + end.toLocaleString();
        }
    }
    
    requestAnimationFrame(updateNumber);
}

function initializeQuickActions() {
    // Add loading states to quick action buttons
    const quickActionBtns = document.querySelectorAll('.quick-actions .btn');
    
    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            if (this.classList.contains('loading')) {
                e.preventDefault();
                return;
            }
            
            // Add loading state
            this.classList.add('loading');
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            
            // Remove loading state after navigation (fallback)
            setTimeout(() => {
                this.classList.remove('loading');
                this.innerHTML = originalText;
            }, 3000);
        });
    });
}

function viewCourseDetails(courseId) {
    // Open course details in a modal or navigate to course page
    window.open(`/courses/${courseId}`, '_blank');
}

async function refreshStats() {
    try {
        const response = await fetch('/api/instructor/stats', {
            method: 'GET',
            credentials: 'include',
            headers: {
                'Authorization': `Bearer ${getCookie('accessToken')}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            updateStatsDisplay(data.stats);
        }
    } catch (error) {
        console.error('Failed to refresh stats:', error);
    }
}

function updateStatsDisplay(stats) {
    // Update stat cards with new data
    const statElements = {
        totalCourses: document.querySelector('.stat-card:nth-child(1) .stat-content h3'),
        publishedCourses: document.querySelector('.stat-card:nth-child(2) .stat-content h3'),
        draftCourses: document.querySelector('.stat-card:nth-child(3) .stat-content h3'),
        totalStudents: document.querySelector('.stat-card:nth-child(4) .stat-content h3'),
        totalRevenue: document.querySelector('.stat-card:nth-child(5) .stat-content h3'),
        averageRating: document.querySelector('.stat-card:nth-child(6) .stat-content h3')
    };
    
    Object.keys(statElements).forEach(key => {
        const element = statElements[key];
        if (element && stats[key] !== undefined) {
            if (key === 'totalRevenue') {
                element.textContent = '$' + stats[key].toLocaleString();
            } else if (key === 'averageRating') {
                element.textContent = stats[key].toFixed(1);
            } else {
                element.textContent = stats[key].toLocaleString();
            }
        }
    });
}

// Utility function to get cookie value
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
}

// Show success/error messages
function showMessage(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        ${message}
    `;
    
    // Insert at the top of the dashboard
    const dashboard = document.querySelector('.instructor-dashboard');
    dashboard.insertBefore(alertDiv, dashboard.firstChild);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Handle course card interactions
document.addEventListener('click', function(e) {
    if (e.target.closest('.course-card')) {
        const card = e.target.closest('.course-card');
        
        // Add ripple effect
        const ripple = document.createElement('div');
        ripple.className = 'ripple';
        ripple.style.left = e.offsetX + 'px';
        ripple.style.top = e.offsetY + 'px';
        
        card.style.position = 'relative';
        card.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }
});

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
        width: 20px;
        height: 20px;
        margin-left: -10px;
        margin-top: -10px;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .btn.loading {
        opacity: 0.7;
        cursor: not-allowed;
    }
`;
document.head.appendChild(style);

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + N for new course
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        window.location.href = '/instructor/courses/new';
    }
    
    // Ctrl/Cmd + M for manage courses
    if ((e.ctrlKey || e.metaKey) && e.key === 'm') {
        e.preventDefault();
        window.location.href = '/instructor/courses';
    }
});

// Add keyboard shortcut hints
const shortcutHints = document.createElement('div');
shortcutHints.className = 'keyboard-shortcuts';
shortcutHints.innerHTML = `
    <div class="shortcuts-toggle" onclick="toggleShortcuts()">
        <i class="fas fa-keyboard"></i>
    </div>
    <div class="shortcuts-panel" id="shortcutsPanel">
        <h4>Keyboard Shortcuts</h4>
        <div class="shortcut-item">
            <kbd>Ctrl</kbd> + <kbd>N</kbd> - New Course
        </div>
        <div class="shortcut-item">
            <kbd>Ctrl</kbd> + <kbd>M</kbd> - Manage Courses
        </div>
    </div>
`;

// Add shortcuts CSS
const shortcutsStyle = document.createElement('style');
shortcutsStyle.textContent = `
    .keyboard-shortcuts {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
    }
    
    .shortcuts-toggle {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
    }
    
    .shortcuts-toggle:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }
    
    .shortcuts-panel {
        position: absolute;
        bottom: 60px;
        right: 0;
        background: rgba(26, 26, 46, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 1rem;
        min-width: 200px;
        backdrop-filter: blur(10px);
        opacity: 0;
        visibility: hidden;
        transform: translateY(10px);
        transition: all 0.3s ease;
    }
    
    .shortcuts-panel.active {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
    
    .shortcuts-panel h4 {
        margin: 0 0 0.75rem 0;
        color: #ffffff;
        font-size: 0.9rem;
    }
    
    .shortcut-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.85rem;
    }
    
    kbd {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 3px;
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
        color: #ffffff;
    }
`;

document.head.appendChild(shortcutsStyle);
document.body.appendChild(shortcutHints);

function toggleShortcuts() {
    const panel = document.getElementById('shortcutsPanel');
    panel.classList.toggle('active');
}
