<link rel="stylesheet" href="/css/course-form.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<div class="course-form-container">
  <!-- Page Header -->
  <div class="page-header">
    <div class="header-content">
      <h1><%= isEdit ? 'Edit Course' : 'Create New Course' %></h1>
      <p><%= isEdit ? 'Update your course information' : 'Share your knowledge with students worldwide' %></p>
    </div>
    <div class="header-actions">
      <a href="/instructor/courses" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i>
        Back to Courses
      </a>
    </div>
  </div>

  <!-- Error/Success Messages -->
  <% if (typeof error !== 'undefined' && error) { %>
    <div class="alert alert-error">
      <i class="fas fa-exclamation-circle"></i>
      <%= error %>
    </div>
  <% } %>

  <!-- Course Form -->
  <form class="course-form" method="POST" action="<%= isEdit ? `/instructor/courses/${course._id}?_method=PUT` : '/instructor/courses' %>" enctype="multipart/form-data">
    <div class="form-sections">
      
      <!-- Basic Information Section -->
      <div class="form-section">
        <div class="section-header">
          <h2>Basic Information</h2>
          <p>Essential details about your course</p>
        </div>
        
        <div class="form-grid">
          <div class="form-group full-width">
            <label for="title" class="form-label">
              Course Title <span class="required">*</span>
            </label>
            <input 
              type="text" 
              id="title" 
              name="title" 
              class="form-input" 
              value="<%= course ? course.title : '' %>"
              placeholder="Enter an engaging course title"
              required
              maxlength="100"
            >
            <div class="form-help">Make it clear and compelling (max 100 characters)</div>
          </div>
          
          <div class="form-group full-width">
            <label for="description" class="form-label">
              Course Description <span class="required">*</span>
            </label>
            <textarea 
              id="description" 
              name="description" 
              class="form-textarea" 
              rows="6"
              placeholder="Describe what students will learn in this course..."
              required
              maxlength="2000"
            ><%= course ? course.description : '' %></textarea>
            <div class="form-help">Detailed description of your course content (max 2000 characters)</div>
          </div>
          
          <div class="form-group">
            <label for="level" class="form-label">
              Course Level <span class="required">*</span>
            </label>
            <select id="level" name="level" class="form-select" required>
              <option value="">Select Level</option>
              <option value="Beginner" <%= course && course.level === 'Beginner' ? 'selected' : '' %>>Beginner</option>
              <option value="Intermediate" <%= course && course.level === 'Intermediate' ? 'selected' : '' %>>Intermediate</option>
              <option value="Advanced" <%= course && course.level === 'Advanced' ? 'selected' : '' %>>Advanced</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="category" class="form-label">Category</label>
            <input 
              type="text" 
              id="category" 
              name="category" 
              class="form-input" 
              value="<%= course ? course.category : '' %>"
              placeholder="e.g., Programming, Design, Business"
              maxlength="50"
            >
          </div>
          
          <div class="form-group">
            <label for="duration" class="form-label">
              Duration (hours) <span class="required">*</span>
            </label>
            <input 
              type="number" 
              id="duration" 
              name="duration" 
              class="form-input" 
              value="<%= course ? course.duration : '' %>"
              placeholder="0"
              min="0.5"
              max="500"
              step="0.5"
              required
            >
          </div>
          
          <div class="form-group">
            <label for="price" class="form-label">
              Price ($) <span class="required">*</span>
            </label>
            <input 
              type="number" 
              id="price" 
              name="price" 
              class="form-input" 
              value="<%= course ? course.price : '' %>"
              placeholder="0"
              min="0"
              step="0.01"
              required
            >
            <div class="form-help">Set to 0 for free courses</div>
          </div>
        </div>
      </div>

      <!-- Course Image Section -->
      <div class="form-section">
        <div class="section-header">
          <h2>Course Image</h2>
          <p>Upload an attractive cover image for your course</p>
        </div>
        
        <div class="image-upload-section">
          <% if (course && course.imageUrl) { %>
            <div class="current-image">
              <img src="<%= course.imageUrl %>" alt="Current course image" class="preview-image">
              <p class="image-label">Current Image</p>
            </div>
          <% } %>
          
          <div class="image-upload">
            <label for="image" class="upload-label">
              <div class="upload-area">
                <i class="fas fa-cloud-upload-alt"></i>
                <p>Click to upload or drag and drop</p>
                <p class="upload-help">PNG, JPG, GIF up to 5MB</p>
              </div>
            </label>
            <input type="file" id="image" name="image" accept="image/*" class="file-input">
          </div>
        </div>
      </div>

      <!-- Additional Details Section -->
      <div class="form-section">
        <div class="section-header">
          <h2>Additional Details</h2>
          <p>Extra information to help students understand your course</p>
        </div>
        
        <div class="form-grid">
          <div class="form-group full-width">
            <label for="tags" class="form-label">Tags</label>
            <input 
              type="text" 
              id="tags" 
              name="tags" 
              class="form-input" 
              value="<%= course && course.tags ? course.tags.join(', ') : '' %>"
              placeholder="javascript, web development, react"
            >
            <div class="form-help">Separate tags with commas (max 10 tags)</div>
          </div>
          
          <div class="form-group">
            <label for="status" class="form-label">Status</label>
            <select id="status" name="status" class="form-select">
              <option value="draft" <%= !course || course.status === 'draft' ? 'selected' : '' %>>Draft</option>
              <option value="published" <%= course && course.status === 'published' ? 'selected' : '' %>>Published</option>
              <option value="archived" <%= course && course.status === 'archived' ? 'selected' : '' %>>Archived</option>
            </select>
            <div class="form-help">Draft courses are not visible to students</div>
          </div>
          
          <div class="form-group">
            <label for="maxEnrollments" class="form-label">Max Enrollments</label>
            <input 
              type="number" 
              id="maxEnrollments" 
              name="maxEnrollments" 
              class="form-input" 
              value="<%= course ? course.maxEnrollments : '' %>"
              placeholder="Leave empty for unlimited"
              min="1"
            >
          </div>
        </div>
        
        <div class="form-group">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              name="isPublic" 
              value="true"
              <%= !course || course.isPublic !== false ? 'checked' : '' %>
            >
            <span class="checkmark"></span>
            Make this course publicly visible
          </label>
        </div>
      </div>

      <!-- Prerequisites Section -->
      <div class="form-section">
        <div class="section-header">
          <h2>Prerequisites & Learning Outcomes</h2>
          <p>Help students understand what they need and what they'll gain</p>
        </div>
        
        <div class="form-group">
          <label for="prerequisites" class="form-label">Prerequisites</label>
          <textarea 
            id="prerequisites" 
            name="prerequisites" 
            class="form-textarea" 
            rows="3"
            placeholder="What should students know before taking this course? (one per line)"
          ><%= course && course.prerequisites ? course.prerequisites.join('\n') : '' %></textarea>
        </div>
        
        <div class="form-group">
          <label for="learningOutcomes" class="form-label">Learning Outcomes</label>
          <textarea 
            id="learningOutcomes" 
            name="learningOutcomes" 
            class="form-textarea" 
            rows="4"
            placeholder="What will students be able to do after completing this course? (one per line)"
          ><%= course && course.learningOutcomes ? course.learningOutcomes.join('\n') : '' %></textarea>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <button type="button" class="btn btn-secondary" onclick="window.history.back()">
        <i class="fas fa-times"></i>
        Cancel
      </button>
      <button type="submit" class="btn btn-primary">
        <i class="fas fa-save"></i>
        <%= isEdit ? 'Update Course' : 'Create Course' %>
      </button>
    </div>
  </form>
</div>

<script src="/js/course-form.js"></script>
