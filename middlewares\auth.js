import jwt from 'jsonwebtoken';
import cookieParser from 'cookie-parser';
import dotenv from 'dotenv';
import User from '../models/usermodel.js';
dotenv.config();
/**
 * Middleware to authenticate user using JWT token.
 * Checks if the token is valid and attaches user info to req.user.
 */
export function authenticate(req, res, next) {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ message: 'Authentication token missing or invalid.' });
    }

    const token = authHeader.split(' ')[1];
    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        req.user = decoded; // decoded should contain userId and possibly role
        next();
    } catch (err) {
        return res.status(401).json({ message: 'Invalid or expired token.' });
    }
}

/**
 * Middleware to authorize user based on role(s).
 * @param {...string} allowedRoles - Roles allowed to access the route
 */
export function authorize(...allowedRoles) {
    return (req, res, next) => {
        if (!req.user || !req.user.role) {
            return res.status(403).json({ message: 'User role not found. Access denied.' });
        }
        if (!allowedRoles.includes(req.user.role)) {
            return res.status(403).json({ message: 'You do not have permission to perform this action.' });
        }
        next();
    };
}

export function verifyRefreshToken(req, res, next) {
    const token = req.cookies.refreshToken;
    if (!token) {
        return res.status(401).json({ message: 'Refresh token missing.' });
    }

    try {
        const decoded = jwt.verify(token, process.env.REFRESH_TOKEN_SECRET);
        req.user = decoded;
        next();
    } catch (err) {
        return res.status(403).json({ message: 'Invalid refresh token.' });
    }
}

/**
 * Web authentication middleware for EJS views
 * Checks for JWT token in cookies and redirects to login if not authenticated
 */
export async function authenticateWeb(req, res, next) {
    try {
        // Check for access token in cookies first
        let token = req.cookies.accessToken;

        if (!token) {
            // If no access token, try to get from Authorization header as fallback
            const authHeader = req.headers.authorization;
            if (authHeader && authHeader.startsWith('Bearer ')) {
                token = authHeader.split(' ')[1];
            }
        }

        if (!token) {
            return res.redirect('/login?message=Please log in to access this page');
        }

        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);

            // Get full user details from database
            const user = await User.findById(decoded.userId).select('-password');
            if (!user) {
                return res.redirect('/login?message=User not found');
            }

            req.user = {
                userId: user._id,
                _id: user._id,
                name: user.name,
                email: user.email,
                role: user.role,
                photo: user.photo
            };

            // Make user available to all EJS templates
            res.locals.user = req.user;
            next();
        } catch (tokenError) {
            // Token expired or invalid, try refresh token
            const refreshToken = req.cookies.refreshToken;
            if (refreshToken) {
                try {
                    const refreshDecoded = jwt.verify(refreshToken, process.env.REFRESH_TOKEN_SECRET);
                    const newAccessToken = jwt.sign(
                        { userId: refreshDecoded.userId },
                        process.env.JWT_SECRET,
                        { expiresIn: '15m' }
                    );

                    // Set new access token cookie
                    res.cookie('accessToken', newAccessToken, {
                        httpOnly: true,
                        secure: process.env.NODE_ENV === 'production',
                        sameSite: 'Strict',
                        maxAge: 15 * 60 * 1000 // 15 minutes
                    });

                    // Get user details and continue
                    const user = await User.findById(refreshDecoded.userId).select('-password');
                    if (!user) {
                        return res.redirect('/login?message=User not found');
                    }

                    req.user = {
                        userId: user._id,
                        _id: user._id,
                        name: user.name,
                        email: user.email,
                        role: user.role,
                        photo: user.photo
                    };

                    res.locals.user = req.user;
                    next();
                } catch (refreshError) {
                    return res.redirect('/login?message=Session expired, please log in again');
                }
            } else {
                return res.redirect('/login?message=Session expired, please log in again');
            }
        }
    } catch (error) {
        console.error('Web authentication error:', error);
        return res.redirect('/login?message=Authentication error');
    }
}

/**
 * Web authorization middleware for role-based access control in views
 * @param {...string} allowedRoles - Roles allowed to access the route
 */
export function authorizeWeb(...allowedRoles) {
    return (req, res, next) => {
        if (!req.user || !req.user.role) {
            return res.status(403).render('pages/403', {
                title: 'Access Denied',
                message: 'User role not found. Access denied.'
            });
        }

        if (!allowedRoles.includes(req.user.role)) {
            return res.status(403).render('pages/403', {
                title: 'Access Denied',
                message: 'You do not have permission to access this page.'
            });
        }

        next();
    };
}
