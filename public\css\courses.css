/* Import Font Awesome for icons */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

/* Enhanced Hero Section */
.course-hero {
  text-align: center;
  margin-bottom: 3rem;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, rgba(124, 58, 237, 0.1), rgba(0, 212, 255, 0.1));
  border-radius: 20px;
  border: 1px solid rgba(124, 58, 237, 0.2);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.course-hero::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(124, 58, 237, 0.05),
    transparent,
    rgba(0, 212, 255, 0.05),
    transparent
  );
  animation: shimmer 8s ease-in-out infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(0deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(180deg); }
}

.hero-content {
  position: relative;
  z-index: 1;
}

.course-hero h2 {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 800;
  background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 50%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

.course-hero p {
  font-size: 1.2rem;
  color: #b0b8d1;
  font-weight: 300;
  max-width: 600px;
  margin: 0 auto;
}

/* Enhanced Search Section */
.course-search-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
  width: 100%;
}

.search-container {
  position: relative;
  width: 100%;
  max-width: 700px;
}

#course-search-form {
  display: flex;
  gap: 0.8rem;
  width: 100%;
  position: relative;
  background: rgba(24, 25, 43, 0.8);
  backdrop-filter: blur(20px);
  padding: 0.8rem;
  border-radius: 20px;
  border: 2px solid rgba(124, 58, 237, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#course-search-form:focus-within {
  border-color: #00d4ff;
  box-shadow: 0 0 0 4px rgba(0, 212, 255, 0.15), 0 12px 40px rgba(0, 0, 0, 0.4);
  transform: translateY(-2px);
}

.search-input-wrapper {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 1.2rem;
  color: #7c3aed;
  font-size: 1.1rem;
  z-index: 2;
  transition: all 0.3s ease;
  pointer-events: none;
}

#course-search-form:focus-within .search-icon {
  color: #00d4ff;
  transform: scale(1.1);
}

#course-search-input {
  width: 100%;
  padding: 1.2rem 1.2rem 1.2rem 3.2rem;
  border-radius: 16px;
  border: none;
  font-size: 1.1rem;
  background: rgba(35, 35, 74, 0.6);
  color: #e0e6ed;
  outline: none;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 400;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;
}

#course-search-input::placeholder {
  color: #8b8ba7;
  opacity: 1;
  font-style: italic;
  font-weight: 300;
}

#course-search-input:focus {
  background: rgba(35, 35, 74, 0.8);
}

.clear-search {
  position: absolute;
  right: 1.2rem;
  color: #8b8ba7;
  cursor: pointer;
  font-size: 1rem;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 2;
  padding: 0.2rem;
  border-radius: 50%;
}

.clear-search.visible {
  opacity: 1;
}

.clear-search:hover {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  transform: scale(1.1);
}

#course-search-btn {
  padding: 1.2rem 2rem;
  border-radius: 16px;
  border: none;
  background: linear-gradient(135deg, #7c3aed 0%, #00d4ff 100%);
  color: #fff;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(124, 58, 237, 0.3);
  font-family: inherit;
  letter-spacing: 0.01em;
  outline: none;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

#course-search-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

#course-search-btn:hover::before {
  left: 100%;
}

#course-search-btn:hover {
  background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 212, 255, 0.4);
}

#course-search-btn:active {
  transform: translateY(0);
}

/* Results Counter */
.results-counter {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #b0b8d1;
  font-size: 1rem;
  padding: 0.8rem;
  background: rgba(124, 58, 237, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(124, 58, 237, 0.2);
}

.results-counter i {
  margin-right: 0.5rem;
  color: #7c3aed;
}

/* Loading and Error States */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #7c3aed;
  font-size: 1.2rem;
  font-weight: 500;
  gap: 1rem;
}

.loading-state i {
  font-size: 2rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-state {
  text-align: center;
  padding: 2rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  margin-bottom: 2rem;
  color: #fecaca;
}

.error-state i {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
  color: #ef4444;
}

.error-details {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-top: 0.5rem;
}

/* Enhanced Courses Grid */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}

.course-card {
  background: linear-gradient(145deg, #1a1a2e, #16213e);
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(124, 58, 237, 0.2);
  position: relative;
  backdrop-filter: blur(10px);
  opacity: 0;
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.course-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #7c3aed, #00d4ff, #f59e0b);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.course-card:hover::before {
  transform: scaleX(1);
}

.course-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  border-color: rgba(0, 212, 255, 0.4);
}

.course-card-header {
  position: relative;
  overflow: hidden;
}

.course-card-img {
  width: 100%;
  height: 220px;
  object-fit: cover;
  background: linear-gradient(135deg, #23234a, #1a1a2e);
  transition: transform 0.4s ease;
}

.course-card:hover .course-card-img {
  transform: scale(1.05);
}

.course-level-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.level-beginner {
  background: rgba(34, 197, 94, 0.9);
  color: #fff;
}

.level-intermediate {
  background: rgba(245, 158, 11, 0.9);
  color: #fff;
}

.level-advanced {
  background: rgba(239, 68, 68, 0.9);
  color: #fff;
}

.course-card-body {
  padding: 1.8rem 1.5rem;
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 1rem;
}

.course-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #00d4ff;
  text-align: center;
  letter-spacing: -0.01em;
  line-height: 1.3;
  margin-bottom: 0.5rem;
}

.course-desc {
  color: #b0b8d1;
  font-size: 1rem;
  line-height: 1.6;
  text-align: center;
  font-weight: 300;
  flex-grow: 1;
}

.course-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 0.8rem 0;
}

.course-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stars {
  display: flex;
  gap: 0.1rem;
  color: #f59e0b;
  font-size: 0.9rem;
}

.rating-number {
  font-size: 0.9rem;
  color: #e0e6ed;
  font-weight: 600;
}

.course-students {
  font-size: 0.85rem;
  color: #8b8ba7;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.course-meta {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  color: #8b8ba7;
  font-size: 0.9rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.course-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
  margin: 0.5rem 0;
}

.course-tag {
  display: inline-block;
  background: linear-gradient(135deg, #7c3aed, #5b21b6);
  color: #fff;
  font-size: 0.8rem;
  border-radius: 20px;
  padding: 0.4rem 0.8rem;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3);
  transition: all 0.3s ease;
  border: 1px solid rgba(124, 58, 237, 0.5);
}

.course-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.5);
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.course-card-footer {
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid rgba(35, 35, 74, 0.5);
  gap: 1rem;
}

.course-instructor {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #7ee787;
  font-weight: 500;
  font-size: 0.95rem;
  flex: 1;
}

.course-price-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.course-price {
  font-size: 1.3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.enroll-btn {
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.enroll-btn:not(.free) {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: #fff;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.enroll-btn.free {
  background: linear-gradient(135deg, #10b981, #059669);
  color: #fff;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.enroll-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

.enroll-btn.free:hover {
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

/* No Results State */
.no-results {
  grid-column: 1 / -1;
  text-align: center;
  padding: 4rem 2rem;
  color: #8b8ba7;
}

.no-results i {
  font-size: 4rem;
  color: #7c3aed;
  margin-bottom: 1.5rem;
  display: block;
}

.no-results h3 {
  font-size: 1.5rem;
  color: #e0e6ed;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.no-results p {
  font-size: 1rem;
  margin-bottom: 2rem;
  color: #b0b8d1;
}

.clear-filters-btn {
  padding: 0.8rem 1.5rem;
  background: linear-gradient(135deg, #7c3aed, #5b21b6);
  color: #fff;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.clear-filters-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

/* Style for the "Featured" badge in @courses.ejs */
.featured-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.3rem;
  background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
  color: #fff;
  font-size: 0.85rem;
  font-weight: 700;
  padding: 0.3rem 0.8rem;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.15);
  text-transform: uppercase;
  letter-spacing: 0.04em;
  margin-left: 0.5rem;
  margin-bottom: 0.2rem;
  border: 1px solid #fbbf24;
}

.featured-badge i {
  color: #fffbe6;
  font-size: 1em;
  margin-right: 0.2em;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .courses-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .course-hero {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
  }
  
  .course-hero h2 {
    font-size: 2rem;
  }
  
  .course-hero p {
    font-size: 1rem;
  }
  
  .search-container {
    max-width: 100%;
    padding: 0 1rem;
  }
  
  #course-search-form {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  
  #course-search-btn {
    width: 100%;
    justify-content: center;
  }
  
  .courses-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 1rem;
  }
  
  .course-card-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .course-price-section {
    align-items: center;
  }
  
  .enroll-btn {
    width: 100%;
    text-align: center;
  }
  
  .course-stats {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .course-hero {
    padding: 1.5rem 0.5rem;
  }
  
  .search-container {
    padding: 0 0.5rem;
  }
  
  .courses-grid {
    padding: 0 0.5rem;
  }
  
  .course-card-body {
    padding: 1.2rem 1rem;
  }
  
  .course-title {
    font-size: 1.2rem;
  }
}



 /* Center the pagination container */
 .pagination-container {
    display: flex !important;
    justify-content: center;
    align-items: center;
    margin: 2em 0 1em 0;
    gap: 0.25em;
    width: 100%;
  }
  /* Pagination button styles */
  .pagination-btn {
    background: #23243a;
    color: #fff;
    border: 1.5px solid #44486a;
    border-radius: 5px;
    padding: 0.35em 0.8em;
    margin: 0 0.1em;
    font-size: 1.08em;
    font-family: inherit;
    cursor: pointer;
    transition: background 0.15s, color 0.15s, border 0.15s;
    box-shadow: 0 1px 2px rgba(0,0,0,0.08);
    outline: none;
    min-width: 2.1em;
    min-height: 2.1em;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  .pagination-btn:disabled,
  .pagination-btn[disabled] {
    background: #18192b;
    color: #888;
    border-color: #2a2b3d;
    cursor: not-allowed;
    opacity: 0.7;
  }
  .pagination-btn.active,
  .pagination-btn:focus-visible {
    background: #3a3b5a;
    color: #ffd700;
    border-color: #ffd700;
    font-weight: bold;
    z-index: 1;
  }
  .pagination-ellipsis {
    color: #aaa;
    font-size: 1.1em;
    padding: 0 0.3em;
    user-select: none;
  }