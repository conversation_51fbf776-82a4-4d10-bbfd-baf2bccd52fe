/* Enhanced Styles and Structure for All Sections */

/* ===== HERO SECTION ===== */
.landing-hero {
  background: linear-gradient(120deg, #1a1a2e 60%, #16213e 100%);
  border-radius: 2rem;
  box-shadow: 0 10px 48px rgba(0,0,0,0.28), 0 2px 12px rgba(124,58,237,0.10);
  margin: 3.5rem auto 2.5rem auto;
  max-width: 1100px;
  padding: 4rem 3rem 3rem 3rem;
  color: #e0e6ed;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: box-shadow 0.3s, background 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.landing-hero::before {
  content: '';
  position: absolute;
  top: -100px; left: 50%; transform: translateX(-50%);
  width: 520px; height: 520px;
  background: radial-gradient(circle, #00d4ff44 0%, transparent 75%);
  z-index: 0;
  pointer-events: none;
  filter: blur(2px);
}
.landing-hero-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 700px;
  margin: 0 auto;
}
.landing-hero h1 {
  font-size: 3.2rem;
  font-weight: 900;
  margin-bottom: 1.2rem;
  background: linear-gradient(90deg, #00d4ff 10%, #7c3aed 90%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 6px 40px rgba(0, 212, 255, 0.15);
  letter-spacing: 0.01em;
  line-height: 1.08;
}
.landing-hero p {
  font-size: 1.5rem;
  line-height: 1.8;
  color: #e0e6ed;
  margin-bottom: 2.5rem;
  opacity: 0.95;
  font-weight: 500;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}
.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}
.cta-btn {
  display: inline-block;
  padding: 1.1rem 2.6rem;
  font-size: 1.18rem;
  font-weight: 800;
  border-radius: 2.5rem;
  border: none;
  background: linear-gradient(90deg, #00d4ff 0%, #7c3aed 100%);
  color: #fff;
  box-shadow: 0 3px 20px rgba(0,212,255,0.15);
  cursor: pointer;
  transition: background 0.2s, transform 0.18s, box-shadow 0.2s;
  text-decoration: none;
  letter-spacing: 0.01em;
  outline: none;
  min-width: 160px;
}
.cta-btn.secondary {
  background: rgba(255,255,255,0.09);
  color: #00d4ff;
  border: 2px solid #00d4ff;
  box-shadow: none;
}
.cta-btn:hover, .cta-btn:focus {
  background: linear-gradient(90deg, #7c3aed 0%, #00d4ff 100%);
  color: #fff;
  transform: translateY(-3px) scale(1.04);
  box-shadow: 0 8px 36px rgba(124,58,237,0.15);
}
.cta-btn.secondary:hover, .cta-btn.secondary:focus {
  background: #00d4ff22;
  color: #7c3aed;
  border-color: #7c3aed;
}

/* ===== ABOUT SECTION ===== */
.about-section {
  display: flex;
  flex-direction: row;
  gap: 2.5rem;
  align-items: flex-start;
  justify-content: center;
  max-width: 1100px;
  margin: 0 auto 3.5rem auto;
  padding: 2.5rem 2rem 2rem 2rem;
  background: linear-gradient(120deg, #18192b 60%, #23234a 100%);
  border-radius: 1.3rem;
  box-shadow: 0 4px 24px rgba(0,0,0,0.13);
  border: 2px solid #23263a;
}

.about-content {
  flex: 2 1 340px;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  justify-content: center;
}

.about-content h2 {
  color: #00d4ff;
  font-size: 2rem;
  font-weight: 900;
  margin-bottom: 0.7rem;
  letter-spacing: 0.01em;
}

.about-content p {
  color: #e0e6ed;
  font-size: 1.18rem;
  line-height: 1.7;
  margin-bottom: 0.5rem;
  opacity: 0.96;
}

.about-list {
  margin: 0.7rem 0 0 0;
  padding: 0 0 0 1.2rem;
  color: #7c3aed;
  font-size: 1.08rem;
  list-style: disc inside;
}

.about-list li {
  margin-bottom: 0.3rem;
  color: #e0e6ed;
  opacity: 0.93;
}

.about-visual {
  flex: 1 1 220px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 180px;
  max-width: 320px;
}

.about-visual img,
.about-visual svg {
  max-width: 220px;
  max-height: 220px;
  border-radius: 1rem;
  box-shadow: 0 2px 12px rgba(124,58,237,0.10);
  background: #23234a;
  object-fit: cover;
}

/* ===== FEATURES SECTION ===== */
.features-section {
  margin: 0 auto 3.5rem auto;
  max-width: 1100px;
  display: flex;
  gap: 2.8rem;
  justify-content: center;
  flex-wrap: wrap;
  align-items: stretch;
}
.feature-card {
  background: rgba(30, 34, 54, 0.99);
  border-radius: 1.3rem;
  box-shadow: 0 4px 24px rgba(0,0,0,0.15);
  padding: 2.2rem 2rem 1.7rem 2rem;
  min-width: 260px;
  max-width: 340px;
  flex: 1 1 280px;
  color: #e0e6ed;
  text-align: left;
  position: relative;
  margin-bottom: 1.5rem;
  border: 2px solid #23263a;
  transition: box-shadow 0.22s, border 0.22s, transform 0.18s;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.feature-card:hover {
  box-shadow: 0 12px 40px rgba(0,212,255,0.13), 0 4px 18px rgba(124,58,237,0.13);
  border: 2px solid #00d4ff;
  transform: translateY(-4px) scale(1.025);
}
.feature-icon {
  font-size: 2.4rem;
  margin-bottom: 1rem;
  display: inline-block;
  /* Remove color/gradient for icons */
  background: none;
  -webkit-background-clip: initial;
  -webkit-text-fill-color: initial;
  background-clip: initial;
  filter: none;
  vertical-align: middle;
}
.feature-title {
  font-size: 1.35rem;
  font-weight: 800;
  margin-bottom: 0.6rem;
  color: #7c3aed;
  letter-spacing: 0.01em;
}
.feature-desc {
  font-size: 1.13rem;
  color: #e0e6ed;
  opacity: 0.93;
  line-height: 1.7;
  margin-bottom: 0;
  flex: 1;
}

/* ===== CONTACT SECTION ===== */
.contact-section {
  max-width: 600px;
  margin: 4rem auto 2.5rem auto;
  background: linear-gradient(120deg, #1a1a2e 60%, #23234a 100%);
  border-radius: 1.3rem;
  box-shadow: 0 4px 24px rgba(0,0,0,0.15);
  padding: 2.5rem 2rem;
  border: 2px solid #23263a;
  position: relative;
}
.contact-section::before {
  content: '';
  position: absolute;
  top: -60px; left: 50%; transform: translateX(-50%);
  width: 220px; height: 220px;
  background: radial-gradient(circle, #7c3aed33 0%, transparent 70%);
  z-index: 0;
  pointer-events: none;
  filter: blur(1px);
}
.contact-section h2 {
  text-align: center;
  color: #00d4ff;
  margin-bottom: 1.5rem;
  font-size: 1.7rem;
  font-weight: 900;
  letter-spacing: 0.01em;
  position: relative;
  z-index: 1;
}
.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.3rem;
  position: relative;
  z-index: 1;
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}
.form-group label {
  color: #7c3aed;
  font-weight: 700;
  margin-bottom: 0.3rem;
  display: block;
  font-size: 1.08rem;
  letter-spacing: 0.01em;
}
.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.85rem;
  border-radius: 0.6rem;
  border: 2px solid #23263a;
  background: #18192b;
  color: #e0e6ed;
  font-size: 1.05rem;
  font-family: inherit;
  transition: border 0.18s, box-shadow 0.18s;
  outline: none;
  resize: none;
}
.form-group textarea {
  resize: vertical;
  min-height: 140px;
  max-height: 340px;
}
.form-group input:focus,
.form-group textarea:focus {
  border-color: #00d4ff;
  box-shadow: 0 0 0 2px #00d4ff33;
}
.contact-form button[type="submit"] {
  background: linear-gradient(90deg,#00d4ff 0%,#7c3aed 100%);
  color: #fff;
  font-weight: 800;
  border: none;
  border-radius: 0.6rem;
  padding: 1rem 0;
  font-size: 1.15rem;
  cursor: pointer;
  transition: background 0.2s, transform 0.18s, box-shadow 0.2s;
  margin-top: 0.7rem;
  letter-spacing: 0.01em;
  box-shadow: 0 3px 20px rgba(0,212,255,0.13);
}
.contact-form button[type="submit"]:hover,
.contact-form button[type="submit"]:focus {
  background: linear-gradient(90deg,#7c3aed 0%,#00d4ff 100%);
  box-shadow: 0 8px 36px rgba(124,58,237,0.15);
  transform: translateY(-2px) scale(1.04);
}

/* ===== RESPONSIVE ENHANCEMENTS ===== */
@media (max-width: 1200px) {
  .about-section {
    flex-direction: column;
    gap: 1.5rem;
    padding: 2.2rem 1.2rem 1.7rem 1.2rem;
    max-width: 99vw;
  }
  .about-visual {
    justify-content: flex-start;
    margin-top: 1.2rem;
  }
}
@media (max-width: 1100px) {
  .landing-hero,
  .features-section {
    max-width: 99vw;
    padding-left: 1.2rem;
    padding-right: 1.2rem;
  }
  .features-section {
    gap: 1.5rem;
  }
}
@media (max-width: 900px) {
  .features-section {
    flex-direction: column;
    align-items: center;
    gap: 1.2rem;
  }
  .feature-card {
    max-width: 98vw;
    min-width: 0;
    width: 100%;
  }
  .landing-hero {
    padding: 2.5rem 0.7rem 1.7rem 0.7rem;
    max-width: 99vw;
  }
  .contact-section {
    max-width: 99vw;
    padding: 1.7rem 0.7rem;
  }
  .about-section {
    padding: 1.7rem 0.7rem 1.2rem 0.7rem;
    max-width: 99vw;
  }
  .about-visual img,
  .about-visual svg {
    max-width: 160px;
    max-height: 160px;
  }
}
@media (max-width: 700px) {
  .landing-hero h1 {
    font-size: 2rem;
  }
  .landing-hero p {
    font-size: 1.08rem;
  }
  .feature-title {
    font-size: 1.12rem;
  }
  .feature-desc {
    font-size: 1.01rem;
  }
  .feature-card {
    padding: 1.3rem 0.7rem 1rem 0.7rem;
  }
  .contact-section h2 {
    font-size: 1.22rem;
  }
  .contact-section {
    padding: 1.2rem 0.3rem;
  }
  .form-group label {
    font-size: 0.99rem;
  }
  .form-group input,
  .form-group textarea {
    font-size: 0.99rem;
    padding: 0.6rem;
  }
  .contact-form button[type="submit"] {
    font-size: 1.01rem;
    padding: 0.8rem 0;
  }
  .about-content h2 {
    font-size: 1.3rem;
  }
  .about-content p,
  .about-list {
    font-size: 0.98rem;
  }
  .about-visual img,
  .about-visual svg {
    max-width: 120px;
    max-height: 120px;
  }
}
@media (max-width: 500px) {
  .landing-hero h1 {
    font-size: 1.3rem;
  }
  .landing-hero p {
    font-size: 0.98rem;
  }
  .feature-title {
    font-size: 0.98rem;
  }
  .feature-desc {
    font-size: 0.92rem;
  }
  .feature-card {
    padding: 0.9rem 0.3rem 0.7rem 0.3rem;
  }
  .contact-section h2 {
    font-size: 1rem;
  }
  .contact-section {
    padding: 0.7rem 0.1rem;
  }
  .form-group label {
    font-size: 0.92rem;
  }
  .form-group input,
  .form-group textarea {
    font-size: 0.92rem;
    padding: 0.45rem;
  }
  .contact-form button[type="submit"] {
    font-size: 0.95rem;
    padding: 0.6rem 0;
  }
  .about-content h2 {
    font-size: 1.05rem;
  }
  .about-content p,
  .about-list {
    font-size: 0.92rem;
  }
}