/**
 * @fileoverview Course routes - API endpoints for course operations
 * @module routes/courseRoutes
 *
 * MAIN STUDENT API ENDPOINTS:
 * - GET /api/courses - Shows ALL courses from ALL instructors to students (PUBLIC ACCESS)
 * - GET /api/courses/public - Alternative public endpoint for course browsing
 * - GET /api/courses/:id - View individual course details (PUBLIC ACCESS)
 *
 * INSTRUCTOR API ENDPOINTS:
 * - POST /api/courses - Create new course (INSTRUCTOR ONLY)
 * - PUT /api/courses/:id - Update course (COURSE OWNER ONLY)
 * - DELETE /api/courses/:id - Delete course (COURSE OWNER ONLY)
 * - GET /api/my-courses - Get instructor's own courses (INSTRUCTOR ONLY)
 */

import express from 'express';
import { authenticate, authorize } from '../middlewares/auth.js';
import { upload } from '../middlewares/upload.js';
import {
  createCourse,
  getAllCourses,
  getCourseById,
  updateCourse,
  deleteCourse,
  getMyCourses
} from '../controllers/courseController.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Course:
 *       type: object
 *       required:
 *         - title
 *         - description
 *         - level
 *         - duration
 *         - price
 *       properties:
 *         title:
 *           type: string
 *           maxLength: 100
 *         description:
 *           type: string
 *           maxLength: 2000
 *         level:
 *           type: string
 *           enum: [Beginner, Intermediate, Advanced]
 *         duration:
 *           type: number
 *           minimum: 0.5
 *           maximum: 500
 *         price:
 *           type: number
 *           minimum: 0
 */

// ===== Public Course Routes =====

/**
 * Get all published courses with pagination and filters - MAIN API FOR STUDENTS
 * @route GET /api/courses
 * @access Public (No authentication required - accessible to everyone including students)
 * @param {number} [page=1] - Page number
 * @param {number} [limit=10] - Items per page
 * @param {string} [category] - Filter by category
 * @param {string} [level] - Filter by level
 * @param {string} [search] - Search term
 * @param {string} [tags] - Comma-separated tags
 * @description This is the main API endpoint that shows ALL courses from ALL instructors to students and public users
 *
 * @example
 * GET /api/courses - Get all courses
 * GET /api/courses?page=1&limit=20 - Get first 20 courses
 * GET /api/courses?level=Beginner - Get beginner courses
 * GET /api/courses?search=javascript - Search for javascript courses
 * GET /api/courses?category=Programming&level=Intermediate - Filter by category and level
 */
router.get('/api/courses', getAllCourses);

/**
 * Get all published courses for students (alternative endpoint)
 * @route GET /api/courses/public
 * @access Public (No authentication required)
 * @param {number} [page=1] - Page number
 * @param {number} [limit=10] - Items per page
 * @param {string} [category] - Filter by category
 * @param {string} [level] - Filter by level
 * @param {string} [search] - Search term
 * @param {string} [tags] - Comma-separated tags
 * @description Alternative endpoint specifically for public course browsing
 */
router.get('/api/courses/public', getAllCourses);

/**
 * Get course by ID - Public access for students to view course details
 * @route GET /api/courses/:id
 * @access Public (No authentication required)
 * @param {string} id - Course ID
 * @description Students can view any published course details without authentication
 */
router.get('/api/courses/:id', getCourseById);

// ===== Protected Course Routes (Instructor Only) =====

/**
 * Create a new course
 * @route POST /api/courses
 * @access Private (Instructor only)
 * @body {Object} courseData - Course information
 * @file {File} [image] - Course cover image
 */
router.post('/api/courses',
  authenticate,
  authorize('instructor'),
  upload.single('image'),
  createCourse
);

/**
 * Get instructor's courses with pagination and filters
 * @route GET /api/my-courses
 * @access Private (Instructor only)
 * @param {number} [page=1] - Page number
 * @param {number} [limit=10] - Items per page
 * @param {string} [status] - Filter by status
 * @param {string} [category] - Filter by category
 * @param {string} [level] - Filter by level
 * @param {string} [search] - Search term
 */
router.get('/api/my-courses',
  authenticate,
  authorize('instructor'),
  getMyCourses
);

/**
 * Update course by ID
 * @route PUT /api/courses/:id
 * @access Private (Course owner only)
 * @param {string} id - Course ID
 * @body {Object} updateData - Course update data
 * @file {File} [image] - New course cover image
 */
router.put('/api/courses/:id',
  authenticate,
  authorize('instructor'),
  upload.single('image'),
  updateCourse
);

/**
 * Delete course by ID
 * @route DELETE /api/courses/:id
 * @access Private (Course owner only)
 * @param {string} id - Course ID
 */
router.delete('/api/courses/:id',
  authenticate,
  authorize('instructor'),
  deleteCourse
);

/**
 * Publish course (change status from draft to published)
 * @route PATCH /api/courses/:id/publish
 * @access Private (Course owner only)
 * @param {string} id - Course ID
 */
router.patch('/api/courses/:id/publish',
  authenticate,
  authorize('instructor'),
  (req, res, next) => {
    // Set the status to published and call updateCourse
    req.body = { status: 'published' };
    updateCourse(req, res, next);
  }
);

/**
 * Unpublish course (change status from published to draft)
 * @route PATCH /api/courses/:id/unpublish
 * @access Private (Course owner only)
 * @param {string} id - Course ID
 */
router.patch('/api/courses/:id/unpublish',
  authenticate,
  authorize('instructor'),
  (req, res, next) => {
    // Set the status to draft and call updateCourse
    req.body = { status: 'draft' };
    updateCourse(req, res, next);
  }
);

export default router;