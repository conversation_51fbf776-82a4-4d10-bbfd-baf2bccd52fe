/* Error Pages Styles */
.error-page {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #ffffff;
}

.error-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.error-icon {
  font-size: 4rem;
  color: #ff6b6b;
  margin-bottom: 2rem;
  animation: pulse 2s infinite;
}

.error-code {
  font-size: 6rem;
  font-weight: 900;
  margin: 0;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
}

.error-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 1rem 0;
  color: #ffffff;
}

.error-message {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  transform: translateY(-2px);
}

.error-illustration {
  margin-top: 3rem;
  display: flex;
  justify-content: center;
}

/* Lock Animation for 403 */
.lock-animation {
  position: relative;
  width: 80px;
  height: 80px;
}

.lock-body {
  width: 50px;
  height: 40px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border-radius: 8px;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.lock-body::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

.lock-shackle {
  width: 30px;
  height: 30px;
  border: 6px solid #ff6b6b;
  border-bottom: none;
  border-radius: 30px 30px 0 0;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  animation: shake 1s infinite;
}

/* Warning Animation for general errors */
.warning-animation {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #fdcb6e, #e17055);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  animation: bounce 2s infinite;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(-50%) rotate(0deg);
  }
  25% {
    transform: translateX(-50%) rotate(-5deg);
  }
  75% {
    transform: translateX(-50%) rotate(5deg);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .error-page {
    padding: 1rem;
    min-height: 70vh;
  }
  
  .error-code {
    font-size: 4rem;
  }
  
  .error-title {
    font-size: 1.5rem;
  }
  
  .error-message {
    font-size: 1rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }
  
  .lock-animation,
  .warning-animation {
    width: 60px;
    height: 60px;
  }
  
  .lock-body {
    width: 40px;
    height: 30px;
  }
  
  .lock-shackle {
    width: 24px;
    height: 24px;
    border-width: 4px;
  }
  
  .warning-animation {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .error-code {
    font-size: 3rem;
  }
  
  .error-title {
    font-size: 1.25rem;
  }
  
  .error-icon {
    font-size: 3rem;
  }
}
