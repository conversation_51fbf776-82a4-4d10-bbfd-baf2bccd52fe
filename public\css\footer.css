.site-footer {
  
    background: rgba(15, 15, 35, 0.95);
    border-top: 1px solid rgba(255,255,255,0.1);
    color: #e0e6ed;
    padding: 2rem 0 1.2rem 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    box-shadow: 0 -8px 32px rgba(0,0,0,0.3);
    margin-top: 3rem;
    animation: slideUpFooter 0.8s ease-out;
  }
  @keyframes slideUpFooter {
    from { transform: translateY(100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 1.5rem;
  }
  .footer-brand {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
  }
  .footer-logo {
    font-size: 1.5rem;
    font-weight: 800;
    background: linear-gradient(45deg, #00d4ff, #7c3aed);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
    margin-bottom: 0.2rem;
  }
  .footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 1.2rem;
    align-items: center;
  }
  .footer-link {
    color: #e0e6ed;
    text-decoration: none;
    padding: 0.6rem 1.2rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 1rem;
    background: rgba(255,255,255,0.05);
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    display: inline-flex;
    align-items: center;
    position: relative;
    overflow: hidden;
  }
  .footer-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
  }
  .footer-link:hover::before {
    left: 100%;
  }
  .footer-link:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.3);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.4);
  }
  .footer-extra {
    display: flex;
    flex-wrap: wrap;
    gap: 0.7rem;
    align-items: center;
    font-size: 1rem;
    opacity: 0.85;
  }
  @media (max-width: 768px) {
    .footer-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 1.2rem;
      padding: 0 1.2rem;
    }
    .footer-links {
      gap: 0.7rem;
    }
    .footer-extra {
      font-size: 0.95rem;
    }
  }
  @media (max-width: 480px) {
    .site-footer {
      padding: 1.2rem 0 0.7rem 0;
    }
    .footer-content {
      padding: 0 0.5rem;
    }
    .footer-logo {
      font-size: 1.1rem;
    }
    .footer-link {
      font-size: 0.95rem;
      padding: 0.5rem 0.8rem;
    }
  }