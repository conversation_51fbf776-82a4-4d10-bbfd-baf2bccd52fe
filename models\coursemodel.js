/**
 * @fileoverview Course model with schema definition and methods
 * @module models/Course
 */

import mongoose from 'mongoose';
import { COURSE_LEVELS, COURSE_STATUS, MATERIAL_TYPES } from '../utils/constants.js';

/**
 * Course schema definition
 * @typedef {Object} CourseSchema
 * @property {string} title - Course title
 * @property {string} description - Course description
 * @property {ObjectId} instructor - Reference to instructor user
 * @property {number} price - Course price
 * @property {string[]} tags - Course tags
 * @property {number} duration - Course duration in hours
 * @property {string} level - Course difficulty level
 * @property {string} imageUrl - Course cover image URL
 * @property {number} students - Number of enrolled students (deprecated)
 * @property {number} rating - Course average rating
 * @property {string} status - Course status (draft, published, archived)
 * @property {Object[]} materials - Course materials array
 * @property {number} enrollmentCount - Current enrollment count
 * @property {number} maxEnrollments - Maximum allowed enrollments
 * @property {string} category - Course category
 * @property {string[]} prerequisites - Course prerequisites
 * @property {string[]} learningOutcomes - Expected learning outcomes
 * @property {boolean} isPublic - Whether course is publicly visible
 */
const courseSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Course title is required'],
    trim: true,
    maxlength: [100, 'Course title cannot exceed 100 characters'],
    index: true
  },
  description: {
    type: String,
    required: [true, 'Course description is required'],
    trim: true,
    maxlength: [2000, 'Course description cannot exceed 2000 characters']
  },
  instructor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  price: {
    type: Number,
    required: true,
    default: 0,
    min: [0, 'Price cannot be negative']
  },
  tags: {
    type: [String],
    default: [],
    validate: {
      validator: function(tags) {
        return tags.length <= 10;
      },
      message: 'Cannot have more than 10 tags'
    }
  },
  duration: {
    type: Number, // in hours
    required: [true, 'Course duration is required'],
    min: [0.5, 'Duration must be at least 0.5 hours'],
    max: [500, 'Duration cannot exceed 500 hours']
  },
  level: {
    type: String,
    enum: Object.values(COURSE_LEVELS),
    required: [true, 'Course level is required'],
    index: true
  },
  imageUrl: {
    type: String,
    default: ''
  },
  students: {
    type: Number,
    default: 0,
    min: [0, 'Student count cannot be negative']
  },
  rating: {
    type: Number,
    default: 0,
    min: [0, 'Rating cannot be negative'],
    max: [5, 'Rating cannot exceed 5']
  },
  // New instructor-specific fields
  status: {
    type: String,
    enum: Object.values(COURSE_STATUS),
    default: COURSE_STATUS.DRAFT,
    index: true
  },
  materials: [{
    title: {
      type: String,
      required: true,
      trim: true
    },
    type: {
      type: String,
      enum: Object.values(MATERIAL_TYPES),
      required: true
    },
    url: {
      type: String,
      required: true
    },
    duration: {
      type: Number, // in minutes for videos
      default: 0
    },
    order: {
      type: Number,
      default: 0
    }
  }],
  enrollmentCount: {
    type: Number,
    default: 0,
    min: [0, 'Enrollment count cannot be negative']
  },
  maxEnrollments: {
    type: Number,
    default: null // null means unlimited
  },
  category: {
    type: String,
    trim: true,
    maxlength: [50, 'Category cannot exceed 50 characters']
  },
  prerequisites: {
    type: [String],
    default: []
  },
  learningOutcomes: {
    type: [String],
    default: []
  },
  isPublic: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes for better query performance
courseSchema.index({ instructor: 1, status: 1 });
courseSchema.index({ status: 1, isPublic: 1 });
courseSchema.index({ category: 1, level: 1 });
courseSchema.index({ tags: 1 });
courseSchema.index({ createdAt: -1 });
courseSchema.index({ price: 1 });
courseSchema.index({ rating: -1 });

/**
 * Virtual property for total course duration including materials
 * @virtual totalDuration
 * @returns {number} Total duration in minutes
 *
 * @example
 * const totalTime = course.totalDuration; // Returns total minutes
 */
courseSchema.virtual('totalDuration').get(function() {
  const materialsDuration = this.materials.reduce((total, material) => {
    return total + (material.duration || 0);
  }, 0);
  return this.duration * 60 + materialsDuration; // Convert hours to minutes and add materials duration
});

/**
 * Instance method to check if course enrollment is full
 * @method isFull
 * @returns {boolean} True if course is at maximum capacity
 *
 * @example
 * if (course.isFull()) {
 *   console.log('Course is full');
 * }
 */
courseSchema.methods.isFull = function() {
  return this.maxEnrollments && this.enrollmentCount >= this.maxEnrollments;
};

/**
 * Instance method to check if user can enroll in course
 * @method canEnroll
 * @returns {boolean} True if enrollment is allowed
 *
 * @example
 * if (course.canEnroll()) {
 *   // Allow enrollment
 * }
 */
courseSchema.methods.canEnroll = function() {
  return this.status === COURSE_STATUS.PUBLISHED && this.isPublic && !this.isFull();
};

/**
 * Instance method to calculate course progress percentage
 * @method getProgressPercentage
 * @param {number} [completedMaterials=0] - Number of completed materials
 * @returns {number} Progress percentage (0-100)
 *
 * @example
 * const progress = course.getProgressPercentage(5); // 5 materials completed
 */
courseSchema.methods.getProgressPercentage = function(completedMaterials = 0) {
  if (this.materials.length === 0) return 0;
  return Math.round((completedMaterials / this.materials.length) * 100);
};

/**
 * Instance method to get course summary
 * @method getSummary
 * @returns {Object} Course summary object
 *
 * @example
 * const summary = course.getSummary();
 */
courseSchema.methods.getSummary = function() {
  return {
    id: this._id,
    title: this.title,
    instructor: this.instructor,
    level: this.level,
    duration: this.duration,
    price: this.price,
    rating: this.rating,
    enrollmentCount: this.enrollmentCount,
    status: this.status,
    category: this.category,
    tags: this.tags,
    imageUrl: this.imageUrl
  };
};

/**
 * Static method to find courses by instructor with optional filters
 * @method findByInstructor
 * @param {string} instructorId - Instructor's user ID
 * @param {Object} [filters={}] - Optional filters
 * @param {string} [filters.status] - Course status filter
 * @param {string} [filters.category] - Course category filter
 * @param {string} [filters.level] - Course level filter
 * @returns {Query} Mongoose query object
 *
 * @example
 * const courses = await Course.findByInstructor(instructorId, { status: 'published' });
 */
courseSchema.statics.findByInstructor = function(instructorId, filters = {}) {
  const query = { instructor: instructorId };

  if (filters.status) query.status = filters.status;
  if (filters.category) query.category = filters.category;
  if (filters.level) query.level = filters.level;

  return this.find(query).sort({ createdAt: -1 });
};

/**
 * Static method to find published courses with pagination
 * @method findPublished
 * @param {Object} [options={}] - Query options
 * @param {number} [options.page=1] - Page number
 * @param {number} [options.limit=10] - Items per page
 * @param {Object} [options.filters={}] - Additional filters
 * @returns {Promise<Object>} Paginated results
 *
 * @example
 * const result = await Course.findPublished({ page: 1, limit: 10 });
 */
courseSchema.statics.findPublished = async function(options = {}) {
  const { page = 1, limit = 10, filters = {} } = options;
  const skip = (page - 1) * limit;

  const query = {
    status: COURSE_STATUS.PUBLISHED,
    isPublic: true,
    ...filters
  };

  const [courses, total] = await Promise.all([
    this.find(query)
      .populate('instructor', 'name photo')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit),
    this.countDocuments(query)
  ]);

  return {
    courses,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1
    }
  };
};

/**
 * Static method to get course statistics
 * @method getStats
 * @returns {Promise<Object>} Course statistics
 *
 * @example
 * const stats = await Course.getStats();
 */
courseSchema.statics.getStats = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalRevenue: { $sum: '$price' },
        avgRating: { $avg: '$rating' }
      }
    }
  ]);

  const result = {
    total: 0,
    published: 0,
    draft: 0,
    archived: 0,
    totalRevenue: 0,
    averageRating: 0
  };

  stats.forEach(stat => {
    result.total += stat.count;
    result[stat._id] = stat.count;
    result.totalRevenue += stat.totalRevenue || 0;
    result.averageRating = stat.avgRating || 0;
  });

  return result;
};

/**
 * Pre-save middleware to ensure materials are properly ordered
 * @function
 * @name orderMaterials
 */
courseSchema.pre('save', function(next) {
  if (this.materials && this.materials.length > 0) {
    this.materials.sort((a, b) => a.order - b.order);
  }
  next();
});

/**
 * Course model
 * @class Course
 * @extends mongoose.Model
 */
const Course = mongoose.model('Course', courseSchema);

export default Course;
