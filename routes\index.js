/**
 * @fileoverview Main routes index - combines all route modules and handles landing page
 * @module routes/index
 */

import express from 'express';
import authRoutes from './authRoutes.js';
import userRoutes from './userRoutes.js';
import courseRoutes from './routeCourse.js';

const router = express.Router();

/**
 * @swagger
 * info:
 *   title: JooCourses API
 *   version: 1.0.0
 *   description: Online Learning Platform API
 * servers:
 *   - url: http://localhost:5000
 *     description: Development server
 *   - url: https://localhost:5000
 *     description: Development server (HTTPS)
 * components:
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */

// ===== Landing Page Route =====

/**
 * Professional landing page
 * @route GET /
 * @access Public
 */
router.get('/', (req, res) => {
  try {
    res.render('index', {
      title: 'JooCourses - Professional Online Learning Platform',
      description: 'Master tech skills with expert-led courses, hands-on projects, and career support.',
      currentPage: 'home',
      user: req.user || null
    });
  } catch (error) {
    console.error('Error rendering landing page:', error);
    res.status(500).send('Server Error');
  }
});

// ===== Basic Routes Only (Complex routes temporarily disabled) =====

// ===== API Routes =====

/**
 * Authentication routes
 */
router.use('/', authRoutes);

/**
 * User management routes
 */
router.use('/', userRoutes);

/**
 * Course management routes
 */
router.use('/', courseRoutes);

// ===== Web Routes (EJS Views) - TEMPORARILY DISABLED =====

/**
 * Web interface routes
 * Handles server-side rendered pages and instructor dashboard
 * TEMPORARILY DISABLED to avoid conflicts
 */
// router.use('/', webRoutes);

// ===== Health Check Route =====

/**
 * Health check endpoint
 * @route GET /health
 * @access Public
 * @returns {Object} API health status
 */
router.get('/health', (_req, res) => {
  res.json({
    success: true,
    message: 'API is healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// ===== API Documentation Route =====

/**
 * API documentation endpoint
 * @route GET /api-docs
 * @access Public
 * @returns {Object} API documentation
 */
router.get('/api-docs', (_req, res) => {
  res.json({
    success: true,
    message: 'JooCourses API Documentation',
    version: '1.0.0',
    endpoints: {
      authentication: {
        'POST /api/register': 'Register a new user',
        'POST /api/login': 'Login user',
        'POST /api/logout': 'Logout user',
        'POST /api/refresh-token': 'Refresh access token'
      },
      users: {
        'GET /api/users': 'Get all users (Manager only)',
        'POST /api/users': 'Create user (Manager only)',
        'GET /api/users/:id': 'Get user by ID',
        'PUT /api/users/:id': 'Update user',
        'DELETE /api/users/:id': 'Delete user (Manager only)',
        'GET /api/users/profile': 'Get current user profile',
        'PUT /api/users/profile/photo': 'Update profile photo',
        'PUT /api/users/profile/password': 'Change password'
      },
      courses: {
        'GET /api/courses': 'Get all published courses',
        'POST /api/courses': 'Create course (Instructor only)',
        'GET /api/courses/:id': 'Get course by ID',
        'PUT /api/courses/:id': 'Update course (Owner only)',
        'DELETE /api/courses/:id': 'Delete course (Owner only)',
        'GET /api/my-courses': 'Get instructor courses'
      },
      web: {
        'GET /': 'Home page',
        'GET /login': 'Login page',
        'GET /register': 'Registration page',
        'GET /instructor_Dashboard': 'Instructor dashboard',
        'GET /instructor/courses': 'Instructor courses management',
        'GET /instructor/courses/new': 'Create course form',
        'GET /instructor/courses/:id/edit': 'Edit course form'
      }
    },
    authentication: {
      type: 'Bearer Token',
      header: 'Authorization: Bearer <token>',
      note: 'Include JWT token in Authorization header for protected routes'
    }
  });
});

// ===== 404 Handler for API Routes - TEMPORARILY DISABLED =====

/**
 * Handle 404 errors for API routes - TEMPORARILY DISABLED
 */
// router.use('/api/*', (req, res) => {
//   res.status(404).json({
//     success: false,
//     message: 'API endpoint not found',
//     path: req.originalUrl,
//     method: req.method,
//     timestamp: new Date().toISOString()
//   });
// });

export default router;
