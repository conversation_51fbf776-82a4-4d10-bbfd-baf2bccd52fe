<!-- Instructor Courses Management Page -->
<div class="instructor-courses-page">
  <div class="page-header">
    <div class="container">
      <div class="header-content">
        <div class="header-text">
          <h1 class="page-title">My Courses</h1>
          <p class="page-description">Manage your courses, track performance, and engage with students</p>
        </div>
        <div class="header-actions">
          <a href="/instructor/courses/new" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            <span>Create New Course</span>
          </a>
        </div>
      </div>
    </div>
  </div>

  <div class="page-content">
    <div class="container">
      <!-- Filters and Search -->
      <div class="courses-filters">
        <div class="filters-row">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="courseSearch" placeholder="Search courses..." class="search-input">
          </div>
          
          <div class="filter-group">
            <select id="statusFilter" class="filter-select">
              <option value="">All Status</option>
              <option value="draft">Draft</option>
              <option value="published">Published</option>
              <option value="archived">Archived</option>
            </select>
          </div>
          
          <div class="filter-group">
            <select id="categoryFilter" class="filter-select">
              <option value="">All Categories</option>
              <option value="Programming">Programming</option>
              <option value="Web Development">Web Development</option>
              <option value="Data Science">Data Science</option>
              <option value="Mobile Development">Mobile Development</option>
              <option value="DevOps">DevOps</option>
            </select>
          </div>
          
          <div class="view-toggle">
            <button class="view-btn active" data-view="grid" title="Grid View">
              <i class="fas fa-th"></i>
            </button>
            <button class="view-btn" data-view="list" title="List View">
              <i class="fas fa-list"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Courses Grid/List -->
      <div class="courses-container">
        <div id="coursesGrid" class="courses-grid">
          <!-- Courses will be loaded here -->
          <div class="loading-skeleton">
            <div class="skeleton-card">
              <div class="skeleton-image"></div>
              <div class="skeleton-content">
                <div class="skeleton-title"></div>
                <div class="skeleton-text"></div>
                <div class="skeleton-actions"></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Pagination -->
        <div class="pagination-container" id="paginationContainer">
          <!-- Pagination will be loaded here -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Course Actions Modal -->
<div class="modal" id="courseActionsModal">
  <div class="modal-content">
    <div class="modal-header">
      <h3 class="modal-title">Course Actions</h3>
      <button class="modal-close" onclick="closeModal('courseActionsModal')">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="modal-body">
      <div class="course-info" id="modalCourseInfo">
        <!-- Course info will be populated here -->
      </div>
      <div class="action-buttons">
        <button class="btn btn-primary" onclick="editCourse()">
          <i class="fas fa-edit"></i>
          <span>Edit Course</span>
        </button>
        <button class="btn btn-success" onclick="toggleCourseStatus()" id="statusToggleBtn">
          <i class="fas fa-eye"></i>
          <span>Publish Course</span>
        </button>
        <button class="btn btn-info" onclick="viewCourseStats()">
          <i class="fas fa-chart-bar"></i>
          <span>View Analytics</span>
        </button>
        <button class="btn btn-danger" onclick="deleteCourse()">
          <i class="fas fa-trash"></i>
          <span>Delete Course</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal" id="deleteConfirmModal">
  <div class="modal-content">
    <div class="modal-header">
      <h3 class="modal-title">Confirm Deletion</h3>
      <button class="modal-close" onclick="closeModal('deleteConfirmModal')">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="modal-body">
      <div class="warning-message">
        <i class="fas fa-exclamation-triangle"></i>
        <p>Are you sure you want to delete this course? This action cannot be undone.</p>
      </div>
      <div class="confirmation-actions">
        <button class="btn btn-secondary" onclick="closeModal('deleteConfirmModal')">Cancel</button>
        <button class="btn btn-danger" onclick="confirmDeleteCourse()">
          <i class="fas fa-trash"></i>
          <span>Delete Course</span>
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// Global variables
let currentCourses = [];
let currentPage = 1;
let totalPages = 1;
let selectedCourse = null;

// Load instructor courses
async function loadInstructorCourses(page = 1, filters = {}) {
  try {
    showLoading();
    
    const params = new URLSearchParams({
      page: page,
      limit: 12,
      ...filters
    });
    
    const response = await fetch(`/courses/api/my-courses?${params}`, {
      headers: {
        'Authorization': `Bearer ${getAccessToken()}`
      }
    });
    
    const data = await response.json();
    
    if (data.success) {
      currentCourses = data.data;
      currentPage = data.meta.pagination.currentPage;
      totalPages = data.meta.pagination.totalPages;
      
      renderCourses(currentCourses);
      renderPagination();
    } else {
      showError('Failed to load courses');
    }
  } catch (error) {
    console.error('Error loading courses:', error);
    showError('Failed to load courses');
  } finally {
    hideLoading();
  }
}

// Render courses in grid/list view
function renderCourses(courses) {
  const container = document.getElementById('coursesGrid');
  const viewMode = document.querySelector('.view-btn.active').dataset.view;
  
  if (courses.length === 0) {
    container.innerHTML = `
      <div class="empty-state">
        <i class="fas fa-book-open"></i>
        <h3>No courses found</h3>
        <p>Create your first course to get started</p>
        <a href="/instructor/courses/new" class="btn btn-primary">
          <i class="fas fa-plus"></i>
          <span>Create Course</span>
        </a>
      </div>
    `;
    return;
  }
  
  container.className = `courses-${viewMode}`;
  container.innerHTML = courses.map(course => `
    <div class="course-card" data-course-id="${course._id}">
      <div class="course-image">
        <img src="${course.imageUrl || '/images/course-placeholder.jpg'}" alt="${course.title}">
        <div class="course-status status-${course.status}">${course.status}</div>
        <div class="course-actions">
          <button class="action-btn" onclick="openCourseActions('${course._id}')" title="More Actions">
            <i class="fas fa-ellipsis-v"></i>
          </button>
        </div>
      </div>
      <div class="course-content">
        <h3 class="course-title">${course.title}</h3>
        <p class="course-description">${course.description.substring(0, 100)}...</p>
        <div class="course-meta">
          <span class="course-level">${course.level}</span>
          <span class="course-duration">${course.duration}h</span>
          <span class="course-price">$${course.price}</span>
        </div>
        <div class="course-stats">
          <span class="stat-item">
            <i class="fas fa-users"></i>
            <span>${course.enrollmentCount || 0} students</span>
          </span>
          <span class="stat-item">
            <i class="fas fa-star"></i>
            <span>${course.rating || 0}/5</span>
          </span>
        </div>
        <div class="course-actions-footer">
          <a href="/instructor/courses/${course._id}/edit" class="btn btn-outline btn-small">
            <i class="fas fa-edit"></i>
            <span>Edit</span>
          </a>
          <button class="btn btn-primary btn-small" onclick="toggleCourseStatus('${course._id}', '${course.status}')">
            <i class="fas fa-${course.status === 'published' ? 'eye-slash' : 'eye'}"></i>
            <span>${course.status === 'published' ? 'Unpublish' : 'Publish'}</span>
          </button>
        </div>
      </div>
    </div>
  `).join('');
}

// Get access token from cookies
function getAccessToken() {
  return document.cookie
    .split('; ')
    .find(row => row.startsWith('accessToken='))
    ?.split('=')[1];
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
  loadInstructorCourses();
  
  // Search functionality
  document.getElementById('courseSearch').addEventListener('input', debounce(handleSearch, 300));
  
  // Filter functionality
  document.getElementById('statusFilter').addEventListener('change', handleFilters);
  document.getElementById('categoryFilter').addEventListener('change', handleFilters);
  
  // View toggle
  document.querySelectorAll('.view-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
      this.classList.add('active');
      renderCourses(currentCourses);
    });
  });
});

// Utility functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

function showLoading() {
  // Implementation for loading state
}

function hideLoading() {
  // Implementation for hiding loading state
}

function showError(message) {
  // Implementation for showing error messages
  console.error(message);
}
</script>
