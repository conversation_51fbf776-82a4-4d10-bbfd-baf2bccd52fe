<!-- Flash Messages Component -->
<% if (messages && Object.keys(messages).length > 0) { %>
  <div class="flash-messages-container">
    <% Object.keys(messages).forEach(function(type) { %>
      <% messages[type].forEach(function(message) { %>
        <div class="flash-message flash-<%= type %>" role="alert" aria-live="polite">
          <div class="flash-content">
            <% if (type === 'success') { %>
              <i class="fas fa-check-circle flash-icon"></i>
            <% } else if (type === 'error') { %>
              <i class="fas fa-exclamation-circle flash-icon"></i>
            <% } else if (type === 'warning') { %>
              <i class="fas fa-exclamation-triangle flash-icon"></i>
            <% } else if (type === 'info') { %>
              <i class="fas fa-info-circle flash-icon"></i>
            <% } %>
            <span class="flash-text"><%= message %></span>
          </div>
          <button class="flash-close" onclick="this.parentElement.remove()" aria-label="Close message">
            <i class="fas fa-times"></i>
          </button>
        </div>
      <% }); %>
    <% }); %>
  </div>
<% } %>

<style>
.flash-messages-container {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
}

.flash-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin-bottom: 10px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: slideIn 0.3s ease-out;
}

.flash-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.flash-icon {
  margin-right: 10px;
  font-size: 18px;
}

.flash-text {
  font-weight: 500;
}

.flash-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  margin-left: 10px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.flash-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Message Types */
.flash-success {
  background-color: #d4edda;
  color: #155724;
  border-left: 4px solid #28a745;
}

.flash-error {
  background-color: #f8d7da;
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.flash-warning {
  background-color: #fff3cd;
  color: #856404;
  border-left: 4px solid #ffc107;
}

.flash-info {
  background-color: #d1ecf1;
  color: #0c5460;
  border-left: 4px solid #17a2b8;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Auto-hide messages after 5 seconds */
.flash-message {
  animation: slideIn 0.3s ease-out, fadeOut 0.3s ease-out 4.7s forwards;
}

@keyframes fadeOut {
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}
</style>

<script>
// Auto-remove flash messages after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
  const flashMessages = document.querySelectorAll('.flash-message');
  flashMessages.forEach(function(message) {
    setTimeout(function() {
      if (message.parentElement) {
        message.remove();
      }
    }, 5000);
  });
});
</script>
