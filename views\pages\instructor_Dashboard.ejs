<link rel="stylesheet" href="/css/instructor-dashboard.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<div class="instructor-dashboard">
  <!-- Welcome Section -->
  <div class="welcome-section">
    <div class="welcome-content">
      <div class="instructor-info">
        <div class="instructor-avatar">
          <% if (user.photo) { %>
            <img src="<%= user.photo %>" alt="<%= user.name %>" class="avatar-img">
          <% } else { %>
            <div class="avatar-placeholder">
              <i class="fas fa-user"></i>
            </div>
          <% } %>
        </div>
        <div class="instructor-details">
          <h1>Welcome back, <%= user.name %>!</h1>
          <p class="instructor-role">Instructor Dashboard</p>
          <p class="last-login">Ready to inspire and educate</p>
        </div>
      </div>
      <div class="quick-actions">
        <a href="/instructor/courses/new" class="btn btn-primary">
          <i class="fas fa-plus"></i>
          Create New Course
        </a>
        <a href="/instructor/courses" class="btn btn-secondary">
          <i class="fas fa-list"></i>
          Manage Courses
        </a>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="stats-section">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-book"></i>
        </div>
        <div class="stat-content">
          <h3><%= stats.totalCourses || 0 %></h3>
          <p>Total Courses</p>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon published">
          <i class="fas fa-eye"></i>
        </div>
        <div class="stat-content">
          <h3><%= stats.publishedCourses || 0 %></h3>
          <p>Published Courses</p>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon draft">
          <i class="fas fa-edit"></i>
        </div>
        <div class="stat-content">
          <h3><%= stats.draftCourses || 0 %></h3>
          <p>Draft Courses</p>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon students">
          <i class="fas fa-users"></i>
        </div>
        <div class="stat-content">
          <h3><%= stats.totalStudents || 0 %></h3>
          <p>Total Students</p>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon revenue">
          <i class="fas fa-dollar-sign"></i>
        </div>
        <div class="stat-content">
          <h3>$<%= (stats.totalRevenue || 0).toLocaleString() %></h3>
          <p>Total Revenue</p>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon rating">
          <i class="fas fa-star"></i>
        </div>
        <div class="stat-content">
          <h3><%= (stats.averageRating || 0).toFixed(1) %></h3>
          <p>Average Rating</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Courses Section -->
  <div class="recent-courses-section">
    <div class="section-header">
      <h2>Recent Courses</h2>
      <a href="/instructor/courses" class="view-all-link">
        View All <i class="fas fa-arrow-right"></i>
      </a>
    </div>

    <% if (recentCourses && recentCourses.length > 0) { %>
      <div class="courses-grid">
        <% recentCourses.forEach(course => { %>
          <div class="course-card">
            <div class="course-header">
              <h3 class="course-title"><%= course.title %></h3>
              <span class="course-status status-<%= course.status %>">
                <%= course.status.charAt(0).toUpperCase() + course.status.slice(1) %>
              </span>
            </div>
            <div class="course-stats">
              <div class="stat-item">
                <i class="fas fa-users"></i>
                <span><%= course.enrollmentCount || 0 %> students</span>
              </div>
              <div class="stat-item">
                <i class="fas fa-star"></i>
                <span><%= (course.rating || 0).toFixed(1) %></span>
              </div>
              <div class="stat-item">
                <i class="fas fa-calendar"></i>
                <span><%= new Date(course.createdAt).toLocaleDateString() %></span>
              </div>
            </div>
            <div class="course-actions">
              <a href="/instructor/courses/<%= course._id %>/edit" class="btn btn-sm btn-outline">
                <i class="fas fa-edit"></i>
                Edit
              </a>
              <button class="btn btn-sm btn-outline" onclick="viewCourseDetails('<%= course._id %>')">
                <i class="fas fa-eye"></i>
                View
              </button>
            </div>
          </div>
        <% }); %>
      </div>
    <% } else { %>
      <div class="empty-state">
        <div class="empty-icon">
          <i class="fas fa-book-open"></i>
        </div>
        <h3>No courses yet</h3>
        <p>Start creating your first course to share your knowledge with students.</p>
        <a href="/instructor/courses/new" class="btn btn-primary">
          <i class="fas fa-plus"></i>
          Create Your First Course
        </a>
      </div>
    <% } %>
  </div>

  <!-- Quick Tips Section -->
  <div class="tips-section">
    <div class="section-header">
      <h2>Quick Tips</h2>
    </div>
    <div class="tips-grid">
      <div class="tip-card">
        <div class="tip-icon">
          <i class="fas fa-lightbulb"></i>
        </div>
        <div class="tip-content">
          <h4>Engage Your Students</h4>
          <p>Use interactive materials and regular assessments to keep students engaged throughout your course.</p>
        </div>
      </div>
      <div class="tip-card">
        <div class="tip-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="tip-content">
          <h4>Track Progress</h4>
          <p>Monitor your course performance and student feedback to continuously improve your content.</p>
        </div>
      </div>
      <div class="tip-card">
        <div class="tip-icon">
          <i class="fas fa-comments"></i>
        </div>
        <div class="tip-content">
          <h4>Stay Connected</h4>
          <p>Respond to student questions promptly and create a supportive learning community.</p>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="/js/instructor-dashboard.js"></script>