.login-wrapper {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  padding: 1rem;
}

.login-container {
  background: rgba(21, 21, 38, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(124, 58, 237, 0.2);
  padding: 3rem 2.5rem;
  border: 1px solid rgba(124, 58, 237, 0.3);
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.login-container:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 25px 70px rgba(0, 0, 0, 0.5),
    0 10px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(124, 58, 237, 0.3);
}

.login-header {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
}

.logo {
  width: 64px;
  height: 64px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #7c3aed 0%, #00d4ff 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: white;
  box-shadow: 0 8px 32px rgba(124, 58, 237, 0.4);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.login-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 50%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  letter-spacing: -0.02em;
}

.login-header p {
  color: #b0b8d1;
  font-size: 1.1rem;
  font-weight: 400;
  line-height: 1.5;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #e0e6ed;
  margin-left: 0.25rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1.25rem;
  color: #7c3aed;
  font-size: 1.1rem;
  z-index: 2;
  pointer-events: none;
  transition: all 0.3s ease;
}

.login-form input {
  width: 100%;
  padding: 1.25rem 1.25rem 1.25rem 3.25rem;
  border-radius: 16px;
  border: 2px solid rgba(124, 58, 237, 0.3);
  font-size: 1rem;
  background: rgba(35, 35, 74, 0.6);
  color: #e0e6ed;
  outline: none;
  font-family: inherit;
  font-weight: 400;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.login-form input:focus {
  border-color: #00d4ff;
  background: rgba(35, 35, 74, 0.8);
  box-shadow: 
    0 0 0 3px rgba(0, 212, 255, 0.2),
    inset 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.login-form input:focus + .input-icon {
  color: #00d4ff;
  transform: scale(1.1);
}

.login-form input::placeholder {
  color: #8b8ba7;
  font-weight: 400;
}

.password-toggle {
  position: absolute;
  right: 1.25rem;
  color: #b0b8d1;
  cursor: pointer;
  font-size: 1.1rem;
  transition: color 0.3s ease;
  z-index: 2;
}

.password-toggle:hover {
  color: #00d4ff;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: -0.5rem 0 0.5rem 0;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #b0b8d1;
}

.remember-me input[type="checkbox"] {
  width: auto;
  margin: 0;
  padding: 0;
}

.forgot-password {
  font-size: 0.9rem;
  color: #00d4ff;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #7c3aed;
  text-decoration: underline;
}

.login-btn {
  padding: 1.25rem 0;
  border-radius: 16px;
  border: none;
  background: linear-gradient(135deg, #7c3aed 0%, #00d4ff 100%);
  color: #fff;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 6px 24px rgba(124, 58, 237, 0.4);
  font-family: inherit;
  letter-spacing: 0.01em;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  position: relative;
  overflow: hidden;
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s ease;
}

.login-btn:hover::before {
  left: 100%;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(124, 58, 237, 0.5);
}

.login-btn:active {
  transform: translateY(0);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  display: none;
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.login-error, .login-success {
  border-radius: 12px;
  padding: 1rem 1.25rem;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  font-weight: 500;
  display: none;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-error {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid #fecaca;
  color: #dc2626;
}

.login-success {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #bbf7d0;
  color: #16a34a;
}

.divider {
  margin: 2rem 0;
  text-align: center;
  position: relative;
  color: #8b8ba7;
  font-size: 0.9rem;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(124, 58, 237, 0.3), transparent);
}

.divider span {
  background: rgba(21, 21, 38, 0.95);
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

.social-login {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.social-btn {
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: #ffffff;
  color: #374151;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.social-btn:hover {
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.signup-link {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(124, 58, 237, 0.3);
  color: #b0b8d1;
  font-size: 0.95rem;
}

.signup-link a {
  color: #00d4ff;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.signup-link a:hover {
  color: #7c3aed;
  text-decoration: underline;
}

/* Security tips */
.security-tips {
  margin-top: 1.5rem;
  padding: 1.25rem;
  background: rgba(35, 35, 74, 0.6);
  border-radius: 16px;
  border: 1px solid rgba(124, 58, 237, 0.2);
}

.security-tips h4 {
  color: #e0e6ed;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.security-tips ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.security-tips li {
  color: #b0b8d1;
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  line-height: 1.4;
}

.security-tips li::before {
  content: '✓';
  color: #10b981;
  font-weight: bold;
  margin-top: 0.1rem;
  flex-shrink: 0;
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .login-wrapper {
    padding: 0.5rem;
  }
  
  .login-container {
    padding: 2rem 1.5rem;
    border-radius: 20px;
  }
  
  .login-header h2 {
    font-size: 2rem;
  }
  
  .social-login {
    grid-template-columns: 1fr;
  }
  
  .login-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .login-wrapper, .login-container, .logo, .login-btn, .loading-spinner, .login-error, .login-success {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}