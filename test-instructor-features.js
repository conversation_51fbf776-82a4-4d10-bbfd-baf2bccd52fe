// Test script for Instructor Dashboard features
// Run this with: node test-instructor-features.js

import https from 'https';
import fs from 'fs';

// Disable SSL verification for localhost testing
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const BASE_URL = 'https://localhost:5000';

// Test data
const testInstructor = {
  name: 'Test Instructor',
  email: '<EMAIL>',
  password: 'password123',
  role: 'instructor'
};

const testCourse = {
  title: 'Test Course',
  description: 'This is a test course for the instructor dashboard',
  level: 'Beginner',
  duration: 10,
  price: 99.99,
  category: 'Programming',
  tags: 'javascript, web development, testing',
  status: 'draft'
};

let accessToken = '';

async function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data: body, headers: res.headers });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testUserRegistration() {
  console.log('🧪 Testing user registration...');
  try {
    const response = await makeRequest('POST', '/api/register', testInstructor);
    if (response.status === 201 || response.status === 400) {
      console.log('✅ User registration test passed');
      return true;
    } else {
      console.log('❌ User registration failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ User registration error:', error.message);
    return false;
  }
}

async function testUserLogin() {
  console.log('🧪 Testing user login...');
  try {
    const response = await makeRequest('POST', '/api/login', {
      email: testInstructor.email,
      password: testInstructor.password
    });
    
    if (response.status === 200 && response.data.accessToken) {
      accessToken = response.data.accessToken;
      console.log('✅ User login test passed');
      return true;
    } else {
      console.log('❌ User login failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ User login error:', error.message);
    return false;
  }
}

async function testInstructorDashboardAccess() {
  console.log('🧪 Testing instructor dashboard access...');
  try {
    const response = await makeRequest('GET', '/instructor_Dashboard', null, {
      'Cookie': `accessToken=${accessToken}`
    });
    
    if (response.status === 200 || response.status === 302) {
      console.log('✅ Instructor dashboard access test passed');
      return true;
    } else {
      console.log('❌ Instructor dashboard access failed:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Instructor dashboard access error:', error.message);
    return false;
  }
}

async function testCourseCreation() {
  console.log('🧪 Testing course creation...');
  try {
    const response = await makeRequest('POST', '/courses/api/courses', testCourse, {
      'Authorization': `Bearer ${accessToken}`
    });
    
    if (response.status === 201) {
      console.log('✅ Course creation test passed');
      return response.data.data._id; // Return course ID for further tests
    } else {
      console.log('❌ Course creation failed:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Course creation error:', error.message);
    return null;
  }
}

async function testCourseRetrieval(courseId) {
  console.log('🧪 Testing course retrieval...');
  try {
    const response = await makeRequest('GET', '/courses/api/my-courses', null, {
      'Authorization': `Bearer ${accessToken}`
    });
    
    if (response.status === 200 && response.data.data && Array.isArray(response.data.data)) {
      console.log('✅ Course retrieval test passed');
      return true;
    } else {
      console.log('❌ Course retrieval failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Course retrieval error:', error.message);
    return false;
  }
}

async function testCourseUpdate(courseId) {
  if (!courseId) return false;
  
  console.log('🧪 Testing course update...');
  try {
    const updateData = {
      ...testCourse,
      title: 'Updated Test Course',
      status: 'published'
    };
    
    const response = await makeRequest('PUT', `/courses/api/courses/${courseId}`, updateData, {
      'Authorization': `Bearer ${accessToken}`
    });
    
    if (response.status === 200) {
      console.log('✅ Course update test passed');
      return true;
    } else {
      console.log('❌ Course update failed:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Course update error:', error.message);
    return false;
  }
}

async function testCourseValidation() {
  console.log('🧪 Testing course validation...');
  try {
    const invalidCourse = {
      title: '', // Empty title should fail
      description: 'Test',
      level: 'Invalid', // Invalid level should fail
      duration: -1, // Negative duration should fail
      price: -10 // Negative price should fail
    };
    
    const response = await makeRequest('POST', '/courses/api/courses', invalidCourse, {
      'Authorization': `Bearer ${accessToken}`
    });
    
    if (response.status === 400) {
      console.log('✅ Course validation test passed (correctly rejected invalid data)');
      return true;
    } else {
      console.log('❌ Course validation failed (should have rejected invalid data)');
      return false;
    }
  } catch (error) {
    console.log('❌ Course validation error:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Instructor Dashboard Feature Tests\n');
  
  const tests = [
    { name: 'User Registration', fn: testUserRegistration },
    { name: 'User Login', fn: testUserLogin },
    { name: 'Dashboard Access', fn: testInstructorDashboardAccess },
    { name: 'Course Creation', fn: testCourseCreation },
    { name: 'Course Retrieval', fn: testCourseRetrieval },
    { name: 'Course Validation', fn: testCourseValidation }
  ];
  
  let passed = 0;
  let courseId = null;
  
  for (const test of tests) {
    try {
      const result = await test.fn(courseId);
      if (test.name === 'Course Creation' && result) {
        courseId = result;
      }
      if (result) passed++;
    } catch (error) {
      console.log(`❌ ${test.name} test failed with error:`, error.message);
    }
    console.log(''); // Empty line for readability
  }
  
  // Test course update if we have a course ID
  if (courseId) {
    try {
      const updateResult = await testCourseUpdate(courseId);
      if (updateResult) passed++;
    } catch (error) {
      console.log('❌ Course update test failed with error:', error.message);
    }
  }
  
  console.log(`\n📊 Test Results: ${passed}/${tests.length + (courseId ? 1 : 0)} tests passed`);
  
  if (passed === tests.length + (courseId ? 1 : 0)) {
    console.log('🎉 All tests passed! Instructor Dashboard implementation is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.');
  }
}

// Run tests
runTests().catch(console.error);
