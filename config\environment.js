/**
 * @fileoverview Environment configuration and validation
 * @module config/environment
 */

import dotenv from 'dotenv';
import { ENVIRONMENTS } from '../utils/constants.js';

// Load environment variables
dotenv.config();

/**
 * Validate required environment variables
 * @function validateEnvironment
 * @throws {Error} When required environment variables are missing
 */
const validateEnvironment = () => {
  const required = [
    'MONGO_URI',
    'JWT_SECRET',
    'REFRESH_TOKEN_SECRET',
    'PORT'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  // Validate JWT secrets are strong enough
  if (process.env.JWT_SECRET.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long');
  }

  if (process.env.REFRESH_TOKEN_SECRET.length < 32) {
    throw new Error('REFRESH_TOKEN_SECRET must be at least 32 characters long');
  }
};

/**
 * Environment configuration object
 * @type {Object}
 */
const config = {
  // Application settings
  app: {
    name: process.env.APP_NAME || 'JooCourses',
    version: process.env.APP_VERSION || '1.0.0',
    port: parseInt(process.env.PORT) || 5000,
    host: process.env.HOST || 'localhost',
    environment: process.env.NODE_ENV || ENVIRONMENTS.DEVELOPMENT,
    url: process.env.APP_URL || `http://localhost:${process.env.PORT || 5000}`
  },

  // Database configuration
  database: {
    uri: process.env.MONGO_URI,
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: parseInt(process.env.DB_MAX_POOL_SIZE) || 10,
      serverSelectionTimeoutMS: parseInt(process.env.DB_TIMEOUT) || 5000,
      socketTimeoutMS: 45000,
      family: 4
    }
  },

  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET,
    refreshSecret: process.env.REFRESH_TOKEN_SECRET,
    accessTokenExpiry: process.env.JWT_EXPIRES_IN || '15m',
    refreshTokenExpiry: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d',
    issuer: process.env.JWT_ISSUER || 'joocourses',
    audience: process.env.JWT_AUDIENCE || 'joocourses-users'
  },

  // Session configuration
  session: {
    secret: process.env.SESSION_SECRET || process.env.JWT_SECRET,
    name: process.env.SESSION_NAME || 'joocourses.sid',
    maxAge: parseInt(process.env.SESSION_MAX_AGE) || 24 * 60 * 60 * 1000, // 24 hours
    secure: process.env.NODE_ENV === ENVIRONMENTS.PRODUCTION,
    httpOnly: true,
    sameSite: 'strict'
  },

  // File upload configuration
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024, // 5MB
    allowedImageTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
    allowedDocumentTypes: ['application/pdf', 'application/msword'],
    uploadDir: process.env.UPLOAD_DIR || 'public/uploads',
    tempDir: process.env.TEMP_DIR || 'temp'
  },

  // Email configuration (if needed)
  email: {
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT) || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    user: process.env.EMAIL_USER,
    password: process.env.EMAIL_PASSWORD,
    from: process.env.EMAIL_FROM || '<EMAIL>'
  },

  // OAuth configuration
  oauth: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL: process.env.GOOGLE_CALLBACK_URL || '/auth/google/callback'
    }
  },

  // Security configuration
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 10,
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW) || 15 * 60 * 1000, // 15 minutes
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX) || 100,
    corsOrigin: process.env.CORS_ORIGIN || '*',
    trustProxy: process.env.TRUST_PROXY === 'true'
  },

  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined',
    file: process.env.LOG_FILE,
    maxSize: process.env.LOG_MAX_SIZE || '10m',
    maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5
  },

  // Cache configuration
  cache: {
    ttl: parseInt(process.env.CACHE_TTL) || 300, // 5 minutes
    checkPeriod: parseInt(process.env.CACHE_CHECK_PERIOD) || 600, // 10 minutes
    maxKeys: parseInt(process.env.CACHE_MAX_KEYS) || 1000
  },

  // Feature flags
  features: {
    enableRegistration: process.env.ENABLE_REGISTRATION !== 'false',
    enableGoogleAuth: process.env.ENABLE_GOOGLE_AUTH === 'true',
    enableEmailVerification: process.env.ENABLE_EMAIL_VERIFICATION === 'true',
    enableFileUploads: process.env.ENABLE_FILE_UPLOADS !== 'false',
    enableRateLimiting: process.env.ENABLE_RATE_LIMITING !== 'false'
  }
};

/**
 * Get configuration for current environment
 * @function getConfig
 * @returns {Object} Configuration object
 * 
 * @example
 * import { getConfig } from './config/environment.js';
 * const config = getConfig();
 * console.log(config.app.port);
 */
export const getConfig = () => {
  return config;
};

/**
 * Check if running in development environment
 * @function isDevelopment
 * @returns {boolean} True if in development
 */
export const isDevelopment = () => {
  return config.app.environment === ENVIRONMENTS.DEVELOPMENT;
};

/**
 * Check if running in production environment
 * @function isProduction
 * @returns {boolean} True if in production
 */
export const isProduction = () => {
  return config.app.environment === ENVIRONMENTS.PRODUCTION;
};

/**
 * Check if running in test environment
 * @function isTest
 * @returns {boolean} True if in test
 */
export const isTest = () => {
  return config.app.environment === ENVIRONMENTS.TEST;
};

/**
 * Get database URI with fallback
 * @function getDatabaseUri
 * @returns {string} Database connection URI
 */
export const getDatabaseUri = () => {
  if (isTest()) {
    return process.env.MONGO_URI_TEST || config.database.uri + '_test';
  }
  return config.database.uri;
};

// Validate environment on module load
try {
  validateEnvironment();
  console.log(`🔧 Environment: ${config.app.environment}`);
  console.log(`🚀 Application: ${config.app.name} v${config.app.version}`);
} catch (error) {
  console.error('❌ Environment validation failed:', error.message);
  process.exit(1);
}

export default config;
