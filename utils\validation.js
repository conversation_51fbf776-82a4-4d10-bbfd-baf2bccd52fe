/**
 * @fileoverview Validation utilities and helpers
 * @module utils/validation
 */

import { ValidationError } from './errors.js';

/**
 * Validate required fields in request body
 * @param {Object} body - Request body
 * @param {string[]} requiredFields - Array of required field names
 * @throws {ValidationError} When required fields are missing
 * 
 * @example
 * validateRequiredFields(req.body, ['name', 'email', 'password']);
 */
export const validateRequiredFields = (body, requiredFields) => {
  const missingFields = requiredFields.filter(field => 
    !body[field] || (typeof body[field] === 'string' && body[field].trim() === '')
  );

  if (missingFields.length > 0) {
    throw new ValidationError(
      `Missing required fields: ${missingFields.join(', ')}`,
      { missingFields }
    );
  }
};

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if email is valid
 * 
 * @example
 * if (!isValidEmail(user.email)) {
 *   throw new ValidationError('Invalid email format');
 * }
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {Object} Validation result with isValid and errors
 * 
 * @example
 * const validation = validatePassword(password);
 * if (!validation.isValid) {
 *   throw new ValidationError('Password validation failed', validation.errors);
 * }
 */
export const validatePassword = (password) => {
  const errors = [];
  
  if (!password || password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }
  
  if (!/(?=.*[a-z])/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/(?=.*[A-Z])/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/(?=.*\d)/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate MongoDB ObjectId format
 * @param {string} id - ID to validate
 * @returns {boolean} True if ID is valid ObjectId
 * 
 * @example
 * if (!isValidObjectId(userId)) {
 *   throw new ValidationError('Invalid user ID format');
 * }
 */
export const isValidObjectId = (id) => {
  const objectIdRegex = /^[0-9a-fA-F]{24}$/;
  return objectIdRegex.test(id);
};

/**
 * Validate course data
 * @param {Object} courseData - Course data to validate
 * @throws {ValidationError} When course data is invalid
 * 
 * @example
 * validateCourseData(req.body);
 */
export const validateCourseData = (courseData) => {
  const { title, description, level, duration, price } = courseData;

  // Required fields
  validateRequiredFields(courseData, ['title', 'description', 'level', 'duration']);

  // Title validation
  if (title && title.length > 100) {
    throw new ValidationError('Course title cannot exceed 100 characters');
  }

  // Description validation
  if (description && description.length > 2000) {
    throw new ValidationError('Course description cannot exceed 2000 characters');
  }

  // Level validation
  const validLevels = ['Beginner', 'Intermediate', 'Advanced'];
  if (level && !validLevels.includes(level)) {
    throw new ValidationError(`Invalid course level. Must be one of: ${validLevels.join(', ')}`);
  }

  // Duration validation
  if (duration !== undefined) {
    const durationNum = parseFloat(duration);
    if (isNaN(durationNum) || durationNum < 0.5 || durationNum > 500) {
      throw new ValidationError('Course duration must be between 0.5 and 500 hours');
    }
  }

  // Price validation
  if (price !== undefined) {
    const priceNum = parseFloat(price);
    if (isNaN(priceNum) || priceNum < 0) {
      throw new ValidationError('Course price cannot be negative');
    }
  }

  // Tags validation
  if (courseData.tags) {
    let tags = courseData.tags;
    if (typeof tags === 'string') {
      tags = tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    }
    if (Array.isArray(tags) && tags.length > 10) {
      throw new ValidationError('Maximum 10 tags allowed');
    }
  }

  // Category validation
  if (courseData.category && courseData.category.length > 50) {
    throw new ValidationError('Category cannot exceed 50 characters');
  }

  // Status validation
  if (courseData.status) {
    const validStatuses = ['draft', 'published', 'archived'];
    if (!validStatuses.includes(courseData.status)) {
      throw new ValidationError(`Invalid status. Must be one of: ${validStatuses.join(', ')}`);
    }
  }
};

/**
 * Validate user data
 * @param {Object} userData - User data to validate
 * @throws {ValidationError} When user data is invalid
 * 
 * @example
 * validateUserData(req.body);
 */
export const validateUserData = (userData) => {
  const { name, email, password, role } = userData;

  // Required fields for registration
  if (password) { // Only validate if password is provided (for registration)
    validateRequiredFields(userData, ['name', 'email', 'password']);
  } else {
    validateRequiredFields(userData, ['name', 'email']);
  }

  // Name validation
  if (name && (name.length < 2 || name.length > 50)) {
    throw new ValidationError('Name must be between 2 and 50 characters');
  }

  // Email validation
  if (email && !isValidEmail(email)) {
    throw new ValidationError('Invalid email format');
  }

  // Password validation (only if provided)
  if (password) {
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      throw new ValidationError('Password validation failed', passwordValidation.errors);
    }
  }

  // Role validation
  if (role) {
    const validRoles = ['student', 'instructor', 'manager'];
    if (!validRoles.includes(role)) {
      throw new ValidationError(`Invalid role. Must be one of: ${validRoles.join(', ')}`);
    }
  }

  // Age validation
  if (userData.age !== undefined) {
    const age = parseInt(userData.age);
    if (isNaN(age) || age < 0 || age > 120) {
      throw new ValidationError('Age must be between 0 and 120');
    }
  }
};

/**
 * Sanitize string input
 * @param {string} input - String to sanitize
 * @returns {string} Sanitized string
 * 
 * @example
 * const cleanTitle = sanitizeString(req.body.title);
 */
export const sanitizeString = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/\s+/g, ' '); // Replace multiple spaces with single space
};

/**
 * Validate pagination parameters
 * @param {Object} query - Query parameters
 * @returns {Object} Validated pagination parameters
 * 
 * @example
 * const { page, limit } = validatePagination(req.query);
 */
export const validatePagination = (query) => {
  let { page = 1, limit = 10 } = query;

  page = parseInt(page);
  limit = parseInt(limit);

  if (isNaN(page) || page < 1) {
    page = 1;
  }

  if (isNaN(limit) || limit < 1 || limit > 100) {
    limit = 10;
  }

  return { page, limit };
};
