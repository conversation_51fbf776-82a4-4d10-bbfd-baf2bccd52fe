<!-- Hero Section -->
<section class="hero-section" id="hero">
  <div class="hero-background">
    <div class="hero-overlay"></div>
    <video class="hero-video" autoplay muted loop>
      <source src="/videos/hero-bg.mp4" type="video/mp4">
    </video>
  </div>

  <div class="hero-container">
    <div class="hero-content">
      <div class="hero-badge">
        <i class="fas fa-star"></i>
        <span>Trusted by 10,000+ Students</span>
      </div>

      <h1 class="hero-title">
        Master <span class="highlight">Tech Skills</span> with
        <span class="brand-highlight">JooCourses</span>
      </h1>

      <p class="hero-description">
        Transform your career with industry-leading courses, expert mentorship, and hands-on projects.
        Join thousands of successful graduates who've landed their dream tech jobs.
      </p>

      <div class="hero-stats">
        <div class="stat-item">
          <div class="stat-number">500+</div>
          <div class="stat-label">Courses</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">50+</div>
          <div class="stat-label">Expert Instructors</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">95%</div>
          <div class="stat-label">Success Rate</div>
        </div>
      </div>

      <div class="hero-actions">
        <a href="/courses" class="btn btn-primary btn-large">
          <i class="fas fa-play"></i>
          <span>Start Learning</span>
        </a>
        <a href="#features" class="btn btn-outline btn-large">
          <i class="fas fa-info-circle"></i>
          <span>Learn More</span>
        </a>
      </div>

      <div class="hero-trust">
        <p class="trust-text">Trusted by students from</p>
        <div class="trust-logos">
          <img src="/images/companies/google.png" alt="Google" class="trust-logo">
          <img src="/images/companies/microsoft.png" alt="Microsoft" class="trust-logo">
          <img src="/images/companies/amazon.png" alt="Amazon" class="trust-logo">
          <img src="/images/companies/meta.png" alt="Meta" class="trust-logo">
        </div>
      </div>
    </div>

    <div class="hero-visual">
      <div class="hero-image-container">
        <img src="/images/hero-dashboard.png" alt="JooCourses Dashboard" class="hero-image">
        <div class="floating-card card-1">
          <i class="fas fa-code"></i>
          <span>Interactive Coding</span>
        </div>
        <div class="floating-card card-2">
          <i class="fas fa-certificate"></i>
          <span>Certified Completion</span>
        </div>
        <div class="floating-card card-3">
          <i class="fas fa-users"></i>
          <span>Community Support</span>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Features Section -->
<section class="features-section" id="features">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">Why Choose JooCourses?</h2>
      <p class="section-description">
        We provide everything you need to succeed in your tech career journey
      </p>
    </div>

    <div class="features-grid">
      <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
        <div class="feature-icon">
          <i class="fas fa-chalkboard-teacher"></i>
        </div>
        <h3 class="feature-title">Expert Instructors</h3>
        <p class="feature-description">
          Learn from industry professionals with years of real-world experience and proven teaching expertise.
        </p>
        <ul class="feature-list">
          <li><i class="fas fa-check"></i> Industry veterans</li>
          <li><i class="fas fa-check"></i> Proven track record</li>
          <li><i class="fas fa-check"></i> Continuous support</li>
        </ul>
      </div>

      <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
        <div class="feature-icon">
          <i class="fas fa-project-diagram"></i>
        </div>
        <h3 class="feature-title">Hands-on Projects</h3>
        <p class="feature-description">
          Build real-world projects that showcase your skills and create an impressive portfolio for employers.
        </p>
        <ul class="feature-list">
          <li><i class="fas fa-check"></i> Real-world scenarios</li>
          <li><i class="fas fa-check"></i> Portfolio building</li>
          <li><i class="fas fa-check"></i> Industry standards</li>
        </ul>
      </div>

      <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
        <div class="feature-icon">
          <i class="fas fa-users"></i>
        </div>
        <h3 class="feature-title">Community Support</h3>
        <p class="feature-description">
          Join a vibrant community of learners, get help when you need it, and network with like-minded professionals.
        </p>
        <ul class="feature-list">
          <li><i class="fas fa-check"></i> 24/7 community help</li>
          <li><i class="fas fa-check"></i> Peer networking</li>
          <li><i class="fas fa-check"></i> Study groups</li>
        </ul>
      </div>

      <div class="feature-card" data-aos="fade-up" data-aos-delay="400">
        <div class="feature-icon">
          <i class="fas fa-certificate"></i>
        </div>
        <h3 class="feature-title">Industry Certificates</h3>
        <p class="feature-description">
          Earn recognized certificates that validate your skills and boost your credibility with potential employers.
        </p>
        <ul class="feature-list">
          <li><i class="fas fa-check"></i> Industry recognized</li>
          <li><i class="fas fa-check"></i> Skill validation</li>
          <li><i class="fas fa-check"></i> Career advancement</li>
        </ul>
      </div>

      <div class="feature-card" data-aos="fade-up" data-aos-delay="500">
        <div class="feature-icon">
          <i class="fas fa-clock"></i>
        </div>
        <h3 class="feature-title">Flexible Learning</h3>
        <p class="feature-description">
          Learn at your own pace with lifetime access to course materials and flexible scheduling options.
        </p>
        <ul class="feature-list">
          <li><i class="fas fa-check"></i> Self-paced learning</li>
          <li><i class="fas fa-check"></i> Lifetime access</li>
          <li><i class="fas fa-check"></i> Mobile friendly</li>
        </ul>
      </div>

      <div class="feature-card" data-aos="fade-up" data-aos-delay="600">
        <div class="feature-icon">
          <i class="fas fa-briefcase"></i>
        </div>
        <h3 class="feature-title">Career Support</h3>
        <p class="feature-description">
          Get career guidance, resume reviews, interview preparation, and job placement assistance.
        </p>
        <ul class="feature-list">
          <li><i class="fas fa-check"></i> Resume reviews</li>
          <li><i class="fas fa-check"></i> Interview prep</li>
          <li><i class="fas fa-check"></i> Job placement</li>
        </ul>
      </div>
    </div>
  </div>
</section>

<!-- Popular Courses Section -->
<section class="courses-preview-section" id="courses">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">Popular Courses</h2>
      <p class="section-description">
        Discover our most popular courses that have helped thousands of students achieve their goals
      </p>
    </div>

    <div class="courses-grid" id="popularCourses">
      <!-- Courses will be loaded dynamically -->
      <div class="course-card-skeleton">
        <div class="skeleton-image"></div>
        <div class="skeleton-content">
          <div class="skeleton-title"></div>
          <div class="skeleton-text"></div>
          <div class="skeleton-text"></div>
        </div>
      </div>
    </div>

    <div class="section-footer">
      <a href="/courses" class="btn btn-primary btn-large">
        <span>View All Courses</span>
        <i class="fas fa-arrow-right"></i>
      </a>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section class="testimonials-section" id="testimonials">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">What Our Students Say</h2>
      <p class="section-description">
        Real stories from real students who transformed their careers with JooCourses
      </p>
    </div>

    <div class="testimonials-slider">
      <div class="testimonial-card active">
        <div class="testimonial-content">
          <div class="testimonial-rating">
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
          </div>
          <p class="testimonial-text">
            "JooCourses completely transformed my career. The hands-on projects and expert mentorship
            helped me land my dream job as a software engineer at Google."
          </p>
          <div class="testimonial-author">
            <img src="/images/testimonials/sarah.jpg" alt="Sarah Johnson" class="author-avatar">
            <div class="author-info">
              <h4 class="author-name">Sarah Johnson</h4>
              <p class="author-title">Software Engineer at Google</p>
            </div>
          </div>
        </div>
      </div>

      <div class="testimonial-card">
        <div class="testimonial-content">
          <div class="testimonial-rating">
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
          </div>
          <p class="testimonial-text">
            "The quality of instruction and the practical approach to learning made all the difference.
            I went from zero coding experience to building full-stack applications in just 6 months."
          </p>
          <div class="testimonial-author">
            <img src="/images/testimonials/mike.jpg" alt="Mike Chen" class="author-avatar">
            <div class="author-info">
              <h4 class="author-name">Mike Chen</h4>
              <p class="author-title">Full-Stack Developer at Microsoft</p>
            </div>
          </div>
        </div>
      </div>

      <div class="testimonial-card">
        <div class="testimonial-content">
          <div class="testimonial-rating">
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
          </div>
          <p class="testimonial-text">
            "The community support and career guidance were invaluable. JooCourses doesn't just teach you
            to code - they prepare you for a successful tech career."
          </p>
          <div class="testimonial-author">
            <img src="/images/testimonials/emily.jpg" alt="Emily Rodriguez" class="author-avatar">
            <div class="author-info">
              <h4 class="author-name">Emily Rodriguez</h4>
              <p class="author-title">Data Scientist at Amazon</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="testimonials-navigation">
      <button class="testimonial-nav-btn prev" onclick="changeTestimonial(-1)">
        <i class="fas fa-chevron-left"></i>
      </button>
      <div class="testimonial-dots">
        <span class="dot active" onclick="currentTestimonial(1)"></span>
        <span class="dot" onclick="currentTestimonial(2)"></span>
        <span class="dot" onclick="currentTestimonial(3)"></span>
      </div>
      <button class="testimonial-nav-btn next" onclick="changeTestimonial(1)">
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
  <div class="container">
    <div class="cta-content">
      <h2 class="cta-title">Ready to Start Your Tech Journey?</h2>
      <p class="cta-description">
        Join thousands of successful students and transform your career with JooCourses today.
      </p>
      <div class="cta-actions">
        <a href="/register" class="btn btn-primary btn-large">
          <i class="fas fa-rocket"></i>
          <span>Get Started Free</span>
        </a>
        <a href="/courses" class="btn btn-outline btn-large">
          <i class="fas fa-search"></i>
          <span>Explore Courses</span>
        </a>
      </div>
      <p class="cta-note">
        <i class="fas fa-shield-alt"></i>
        30-day money-back guarantee • No credit card required
      </p>
    </div>
  </div>
</section>

<!-- Page Scripts -->
<script>
// Load popular courses
async function loadPopularCourses() {
  try {
    const response = await fetch('/courses/api/courses?limit=6');
    const data = await response.json();

    if (data.success && data.data.length > 0) {
      const coursesContainer = document.getElementById('popularCourses');
      coursesContainer.innerHTML = data.data.map(course => `
        <div class="course-card" data-aos="fade-up">
          <div class="course-image">
            <img src="${course.imageUrl || '/images/course-placeholder.jpg'}" alt="${course.title}">
            <div class="course-level">${course.level}</div>
          </div>
          <div class="course-content">
            <h3 class="course-title">${course.title}</h3>
            <p class="course-description">${course.description.substring(0, 100)}...</p>
            <div class="course-meta">
              <span class="course-duration"><i class="fas fa-clock"></i> ${course.duration}h</span>
              <span class="course-price">$${course.price}</span>
            </div>
            <div class="course-instructor">
              <img src="${course.instructor.photo || '/images/default-avatar.jpg'}" alt="${course.instructor.name}">
              <span>${course.instructor.name}</span>
            </div>
            <a href="/courses/${course._id}" class="btn btn-primary btn-small">View Course</a>
          </div>
        </div>
      `).join('');
    }
  } catch (error) {
    console.error('Error loading courses:', error);
  }
}

// Testimonials slider
let currentTestimonialIndex = 0;
const testimonials = document.querySelectorAll('.testimonial-card');
const dots = document.querySelectorAll('.dot');

function showTestimonial(index) {
  testimonials.forEach((testimonial, i) => {
    testimonial.classList.toggle('active', i === index);
  });
  dots.forEach((dot, i) => {
    dot.classList.toggle('active', i === index);
  });
}

function changeTestimonial(direction) {
  currentTestimonialIndex += direction;
  if (currentTestimonialIndex >= testimonials.length) currentTestimonialIndex = 0;
  if (currentTestimonialIndex < 0) currentTestimonialIndex = testimonials.length - 1;
  showTestimonial(currentTestimonialIndex);
}

function currentTestimonial(index) {
  currentTestimonialIndex = index - 1;
  showTestimonial(currentTestimonialIndex);
}

// Auto-rotate testimonials
setInterval(() => changeTestimonial(1), 5000);

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
  loadPopularCourses();

  // Initialize AOS (Animate On Scroll) if available
  if (typeof AOS !== 'undefined') {
    AOS.init({
      duration: 800,
      once: true
    });
  }
});
</script>
