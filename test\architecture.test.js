/**
 * @fileoverview Architecture validation tests
 * @module test/architecture
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

/**
 * Test MVC folder structure
 * @function testFolderStructure
 * @description Verify that proper MVC folder structure exists
 */
export const testFolderStructure = () => {
  console.log('🧪 Testing Folder Structure...\n');

  const requiredFolders = [
    'controllers',
    'services', 
    'models',
    'routes',
    'utils',
    'config',
    'middlewares',
    'views',
    'public'
  ];

  const results = [];

  requiredFolders.forEach(folder => {
    const folderPath = path.join(rootDir, folder);
    const exists = fs.existsSync(folderPath);
    results.push({ folder, exists });
    
    if (exists) {
      console.log(`✅ ${folder}/ - exists`);
    } else {
      console.log(`❌ ${folder}/ - missing`);
    }
  });

  const allExist = results.every(r => r.exists);
  console.log(`\n📊 Folder Structure: ${allExist ? '✅ PASS' : '❌ FAIL'}`);
  
  return allExist;
};

/**
 * Test file organization
 * @function testFileOrganization
 * @description Verify that files are properly organized in their respective folders
 */
export const testFileOrganization = () => {
  console.log('\n🧪 Testing File Organization...\n');

  const expectedFiles = {
    'controllers': ['userController.js', 'courseController.js'],
    'services': ['userService.js', 'courseService.js'],
    'models': ['usermodel.js', 'coursemodel.js'],
    'routes': ['index.js', 'authRoutes.js', 'userRoutes.js', 'routeCourse.js'],
    'utils': ['errors.js', 'validation.js', 'helpers.js', 'constants.js', 'sendResponse.js'],
    'config': ['db.js', 'passport.js', 'environment.js', 'middleware.js'],
    'middlewares': ['auth.js', 'upload.js']
  };

  let totalFiles = 0;
  let existingFiles = 0;

  Object.entries(expectedFiles).forEach(([folder, files]) => {
    console.log(`📁 ${folder}/`);
    
    files.forEach(file => {
      totalFiles++;
      const filePath = path.join(rootDir, folder, file);
      const exists = fs.existsSync(filePath);
      
      if (exists) {
        existingFiles++;
        console.log(`  ✅ ${file}`);
      } else {
        console.log(`  ❌ ${file} - missing`);
      }
    });
    
    console.log('');
  });

  const organizationScore = (existingFiles / totalFiles * 100).toFixed(1);
  console.log(`📊 File Organization: ${existingFiles}/${totalFiles} files (${organizationScore}%)`);
  
  return organizationScore >= 80;
};

/**
 * Test JSDoc documentation
 * @function testJSDocDocumentation
 * @description Verify that files have proper JSDoc documentation
 */
export const testJSDocDocumentation = () => {
  console.log('\n🧪 Testing JSDoc Documentation...\n');

  const filesToCheck = [
    'app.js',
    'controllers/userController.js',
    'controllers/courseController.js',
    'services/userService.js',
    'services/courseService.js',
    'utils/errors.js',
    'utils/validation.js',
    'utils/helpers.js',
    'config/db.js'
  ];

  let documentedFiles = 0;

  filesToCheck.forEach(file => {
    const filePath = path.join(rootDir, file);
    
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const hasFileoverview = content.includes('@fileoverview');
      const hasFunctionDocs = content.includes('@function') || content.includes('@async');
      const hasParamDocs = content.includes('@param');
      
      const isDocumented = hasFileoverview && (hasFunctionDocs || hasParamDocs);
      
      if (isDocumented) {
        documentedFiles++;
        console.log(`✅ ${file} - well documented`);
      } else {
        console.log(`❌ ${file} - needs more documentation`);
      }
    } else {
      console.log(`⚠️  ${file} - file not found`);
    }
  });

  const docScore = (documentedFiles / filesToCheck.length * 100).toFixed(1);
  console.log(`\n📊 Documentation: ${documentedFiles}/${filesToCheck.length} files (${docScore}%)`);
  
  return docScore >= 70;
};

/**
 * Test separation of concerns
 * @function testSeparationOfConcerns
 * @description Verify that MVC principles are followed
 */
export const testSeparationOfConcerns = () => {
  console.log('\n🧪 Testing Separation of Concerns...\n');

  const tests = [];

  // Test that controllers don't have direct database queries
  const controllerFiles = ['controllers/userController.js', 'controllers/courseController.js'];
  
  controllerFiles.forEach(file => {
    const filePath = path.join(rootDir, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const hasDirectDbQueries = content.includes('.find(') || content.includes('.create(') || content.includes('.findById(');
      
      if (!hasDirectDbQueries) {
        console.log(`✅ ${file} - no direct database queries`);
        tests.push(true);
      } else {
        console.log(`❌ ${file} - contains direct database queries`);
        tests.push(false);
      }
    }
  });

  // Test that services contain business logic
  const serviceFiles = ['services/userService.js', 'services/courseService.js'];
  
  serviceFiles.forEach(file => {
    const filePath = path.join(rootDir, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const hasBusinessLogic = content.includes('export const') || content.includes('export async');
      
      if (hasBusinessLogic) {
        console.log(`✅ ${file} - contains business logic functions`);
        tests.push(true);
      } else {
        console.log(`❌ ${file} - missing business logic`);
        tests.push(false);
      }
    }
  });

  const passedTests = tests.filter(t => t).length;
  const totalTests = tests.length;
  
  console.log(`\n📊 Separation of Concerns: ${passedTests}/${totalTests} tests passed`);
  
  return passedTests === totalTests;
};

/**
 * Test error handling implementation
 * @function testErrorHandling
 * @description Verify that proper error handling is implemented
 */
export const testErrorHandling = () => {
  console.log('\n🧪 Testing Error Handling Implementation...\n');

  const errorUtilsPath = path.join(rootDir, 'utils/errors.js');
  
  if (!fs.existsSync(errorUtilsPath)) {
    console.log('❌ utils/errors.js - not found');
    return false;
  }

  const content = fs.readFileSync(errorUtilsPath, 'utf8');
  
  const hasCustomErrors = content.includes('ValidationError') && 
                         content.includes('NotFoundError') && 
                         content.includes('AuthenticationError');
  
  const hasGlobalHandler = content.includes('globalErrorHandler');
  const hasCatchAsync = content.includes('catchAsync');

  if (hasCustomErrors) {
    console.log('✅ Custom error classes defined');
  } else {
    console.log('❌ Missing custom error classes');
  }

  if (hasGlobalHandler) {
    console.log('✅ Global error handler implemented');
  } else {
    console.log('❌ Missing global error handler');
  }

  if (hasCatchAsync) {
    console.log('✅ Async error wrapper implemented');
  } else {
    console.log('❌ Missing async error wrapper');
  }

  const errorHandlingScore = [hasCustomErrors, hasGlobalHandler, hasCatchAsync].filter(Boolean).length;
  console.log(`\n📊 Error Handling: ${errorHandlingScore}/3 components implemented`);
  
  return errorHandlingScore === 3;
};

/**
 * Run comprehensive architecture tests
 * @function runArchitectureTests
 * @description Execute all architecture validation tests
 */
export const runArchitectureTests = () => {
  console.log('🚀 Starting Architecture Validation Tests\n');
  console.log('='.repeat(60));

  const results = {
    folderStructure: testFolderStructure(),
    fileOrganization: testFileOrganization(),
    documentation: testJSDocDocumentation(),
    separationOfConcerns: testSeparationOfConcerns(),
    errorHandling: testErrorHandling()
  };

  console.log('\n' + '='.repeat(60));
  console.log('📊 ARCHITECTURE TEST RESULTS');
  console.log('='.repeat(60));

  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    console.log(`${testName.padEnd(25)} ${status}`);
  });

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  const overallScore = (passedTests / totalTests * 100).toFixed(1);

  console.log('='.repeat(60));
  console.log(`🎯 OVERALL SCORE: ${passedTests}/${totalTests} tests passed (${overallScore}%)`);
  
  if (overallScore >= 80) {
    console.log('🎉 Architecture refactoring SUCCESSFUL!');
  } else {
    console.log('⚠️  Architecture needs improvement');
  }

  return results;
};

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runArchitectureTests();
}
