/**
 * @fileoverview Main application entry point - JooCourses Online Learning Platform
 * @module app
 * @version 1.0.0
 * <AUTHOR> Development Team
 * @description Express.js application with MVC architecture, JWT authentication,
 *              and instructor dashboard functionality
 */

import express from 'express';
import dotenv from 'dotenv';
import fs from 'fs';
import https from 'https';
import path from 'path';
import { fileURLToPath } from 'url';
import cookieParser from 'cookie-parser';
import expressLayouts from 'express-ejs-layouts';

// Configuration imports
import connectDB from './config/db.js';
import config from './config/environment.js';

// Route imports
// import mainRoutes from './routes/index.js'; // Enhanced main routes with API endpoints
import legacyRoutes from './routes/router.js'; // Legacy routes with frontend pages
import routerCourse from './routes/routeCourse.js';

// Error handling imports
import { globalErrorHandler } from './utils/errors.js';

// Load environment variables
dotenv.config();

// ES6 module path resolution
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Express application instance
 * @type {Object}
 */
const app = express();

// ===== Middleware Configuration =====

/**
 * Cookie parser middleware for handling cookies
 */
app.use(cookieParser());

/**
 * JSON body parser middleware
 * @description Parses incoming JSON requests
 */
app.use(express.json({ limit: '10mb' }));

/**
 * URL-encoded body parser middleware
 * @description Parses incoming form data
 */
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

/**
 * Method override middleware for HTML forms
 * @description Allows HTML forms to use PUT, DELETE methods via _method field
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
app.use((req, res, next) => {
  if (req.body && typeof req.body === 'object' && '_method' in req.body) {
    const method = req.body._method;
    delete req.body._method;
    req.method = method;
  }
  next();
});

// ===== View Engine Configuration =====

/**
 * Set EJS as the view engine
 * @description Template engine for server-side rendering
 */
app.set('view engine', 'ejs');

/**
 * Set views directory path
 * @description Directory containing EJS template files
 */
app.set('views', path.join(__dirname, 'views'));

/**
 * Static file middleware for uploads
 * @description Serves uploaded files from /uploads route
 */
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

/**
 * Static file middleware for public assets
 * @description Serves CSS, JS, images from public directory
 */
app.use(express.static(path.join(__dirname, 'public')));

/**
 * Express EJS Layouts middleware
 * @description Enables layout support for EJS templates
 */
app.use(expressLayouts);

/**
 * Set default layout template
 * @description Uses layouts/layout.ejs as the default layout
 */
app.set('layout', 'layouts/layout');

// ===== Routes Configuration =====

/**
 * Course-specific routes
 * @description API endpoints for course operations
 */
app.use('/courses/', routerCourse);

/**
 * Main enhanced routes - TEMPORARILY DISABLED
 * @description API routes and basic web routes
 */
// app.use('/', mainRoutes);

/**
 * Legacy routes (for frontend pages)
 * @description Provides all frontend pages and instructor dashboard
 */
app.use('/', legacyRoutes);

// ===== Error Handling =====

/**
 * 404 handler for web routes
 * @description Handles requests to non-existent web pages
 */
app.use((req, res, next) => {
  if (req.path.startsWith('/api/')) {
    return next(); // Let API 404 handler deal with API routes
  }

  res.status(404).render('pages/404', {
    title: 'Page Not Found | JooCourses',
    message: 'The page you are looking for does not exist.',
    user: req.user || null
  });
});

/**
 * Global error handler middleware
 * @description Handles all application errors with proper formatting
 */
app.use(globalErrorHandler);

// ===== Server Configuration =====

/**
 * Server port configuration
 * @type {number}
 * @default 8000
 */
const PORT = process.env.PORT || 8000;

/**
 * HTTPS configuration options
 * @type {Object}
 * @property {Buffer} key - Private key for SSL certificate
 * @property {Buffer} cert - SSL certificate
 */
const httpsOptions = {
  key: fs.readFileSync('./certs/localhost-key.pem'),
  cert: fs.readFileSync('./certs/localhost.pem'),
};

/**
 * Start the HTTPS server
 * @async
 * @function startServer
 * @description Connects to database and starts the HTTPS server
 * @returns {Promise<void>}
 *
 * @example
 * // Server will start automatically when this module is loaded
 * // Access the application at https://localhost:5000
 */
const startServer = async () => {
  try {
    // Connect to MongoDB database
    await connectDB();
    console.log('✅ Database connected successfully');

    // Start HTTPS server
    https.createServer(httpsOptions, app).listen(PORT, () => {
      console.log(`🔐 HTTPS Server running at https://localhost:${PORT}`);
      console.log(`� Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📚 JooCourses Online Learning Platform v${config.app.version}`);
      console.log('🚀 Server is ready to accept connections');
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
};

// ===== Application Startup =====

/**
 * Initialize and start the application
 * @description Entry point for the JooCourses application
 */
startServer();

/**
 * Graceful shutdown handling
 * @description Handles SIGINT and SIGTERM signals for clean shutdown
 */
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT. Graceful shutdown...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM. Graceful shutdown...');
  process.exit(0);
});

/**
 * Unhandled promise rejection handler
 * @param {Error} reason - The rejection reason
 * @param {Promise} promise - The rejected promise
 */
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Promise Rejection:', reason);
  console.error('Promise:', promise);
  process.exit(1);
});

/**
 * Uncaught exception handler
 * @param {Error} error - The uncaught exception
 */
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});
