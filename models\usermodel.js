/**
 * @fileoverview User model with schema definition and methods
 * @module models/User
 */

import mongoose from 'mongoose';
import validator from 'validator';
import bcrypt from 'bcrypt';
import { USER_ROLES, AUTH_TYPES } from '../utils/constants.js';

/**
 * User schema definition
 * @typedef {Object} UserSchema
 * @property {string} name - User's full name
 * @property {string} email - User's email address
 * @property {string} password - User's password (for local auth)
 * @property {string} phone - User's phone number
 * @property {string} role - User's role (student, instructor, manager)
 * @property {string} authType - Authentication type (local, google)
 * @property {string} googleId - Google OAuth ID
 * @property {string} photo - User's profile photo URL
 * @property {number} age - User's age
 * @property {Date} lastLogin - Last login timestamp
 * @property {string} resetPasswordToken - Password reset token
 * @property {Date} resetPasswordExpires - Password reset token expiry
 */
const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'Name is required'],
      trim: true,
      minlength: [2, 'Name must be at least 2 characters long'],
      maxlength: [50, 'Name cannot exceed 50 characters']
    },

    email: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      lowercase: true,
      trim: true,
      validate: [validator.isEmail, 'Please provide a valid email address'],
      index: true
    },

    password: {
      type: String,
      required: function () {
        return this.authType === AUTH_TYPES.LOCAL;
      },
      minlength: [6, 'Password must be at least 6 characters long'],
      select: false // Hide password in query results
    },

    phone: {
      type: String,
      trim: true,
      validate: {
        validator: function (v) {
          return !v || validator.isMobilePhone(v + '', 'any', { strictMode: false });
        },
        message: 'Please provide a valid phone number'
      }
    },

    role: {
      type: String,
      enum: Object.values(USER_ROLES),
      default: USER_ROLES.STUDENT,
      required: [true, 'Role is required'],
      index: true
    },

    authType: {
      type: String,
      enum: Object.values(AUTH_TYPES),
      default: AUTH_TYPES.LOCAL
    },

    googleId: {
      type: String,
      default: null
    },

    photo: {
      type: String,
      default: ''
    },

    age: {
      type: Number,
      min: [0, 'Age must be a positive number'],
      max: [120, 'Age seems unrealistic']
    },

    lastLogin: {
      type: Date
    },

    resetPasswordToken: {
      type: String
    },

    resetPasswordExpires: {
      type: Date
    }

  },
  {
    timestamps: true
  }
);

// Indexes for better query performance
userSchema.index({ email: 1 });
userSchema.index({ role: 1 });
userSchema.index({ authType: 1 });
userSchema.index({ googleId: 1 });

/**
 * Pre-save middleware to normalize email
 * @function
 * @name normalizeEmail
 */
userSchema.pre('save', function (next) {
  if (this.isModified('email')) {
    this.email = validator.normalizeEmail(this.email);
  }
  next();
});

/**
 * Pre-save middleware to hash password
 * @function
 * @name hashPassword
 */
userSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next();
  this.password = await bcrypt.hash(this.password, 10);
  next();
});

/**
 * Instance method to validate password
 * @method isPasswordValid
 * @param {string} inputPassword - Password to validate
 * @returns {Promise<boolean>} True if password is valid
 *
 * @example
 * const isValid = await user.isPasswordValid('userPassword123');
 */
userSchema.methods.isPasswordValid = async function (inputPassword) {
  return bcrypt.compare(inputPassword, this.password);
};

/**
 * Instance method to check user role
 * @method hasRole
 * @param {string} role - Role to check
 * @returns {boolean} True if user has the specified role
 *
 * @example
 * if (user.hasRole('instructor')) {
 *   // User is an instructor
 * }
 */
userSchema.methods.hasRole = function (role) {
  return this.role === role;
};

/**
 * Instance method to get user's full profile (excluding sensitive data)
 * @method getPublicProfile
 * @returns {Object} Public user profile
 *
 * @example
 * const profile = user.getPublicProfile();
 */
userSchema.methods.getPublicProfile = function () {
  const userObject = this.toObject();
  delete userObject.password;
  delete userObject.resetPasswordToken;
  delete userObject.resetPasswordExpires;
  return userObject;
};

/**
 * Instance method to update last login timestamp
 * @method updateLastLogin
 * @returns {Promise<void>}
 *
 * @example
 * await user.updateLastLogin();
 */
userSchema.methods.updateLastLogin = async function () {
  this.lastLogin = new Date();
  return this.save({ validateBeforeSave: false });
};

/**
 * Static method to find user by email
 * @method findByEmail
 * @param {string} email - Email to search for
 * @returns {Promise<Object|null>} User document or null
 *
 * @example
 * const user = await User.findByEmail('<EMAIL>');
 */
userSchema.statics.findByEmail = function (email) {
  return this.findOne({ email: email.toLowerCase() });
};

/**
 * Static method to find users by role
 * @method findByRole
 * @param {string} role - Role to search for
 * @param {Object} [options={}] - Query options
 * @returns {Promise<Array>} Array of user documents
 *
 * @example
 * const instructors = await User.findByRole('instructor');
 */
userSchema.statics.findByRole = function (role, options = {}) {
  const query = this.find({ role });

  if (options.limit) query.limit(options.limit);
  if (options.skip) query.skip(options.skip);
  if (options.sort) query.sort(options.sort);

  return query;
};

/**
 * Static method to get user statistics
 * @method getStats
 * @returns {Promise<Object>} User statistics
 *
 * @example
 * const stats = await User.getStats();
 */
userSchema.statics.getStats = async function () {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$role',
        count: { $sum: 1 }
      }
    }
  ]);

  const result = {
    total: 0,
    students: 0,
    instructors: 0,
    managers: 0
  };

  stats.forEach(stat => {
    result.total += stat.count;
    result[stat._id + 's'] = stat.count;
  });

  return result;
};

/**
 * User model
 * @class User
 * @extends mongoose.Model
 */
const User = mongoose.model('User', userSchema);

export default User;
