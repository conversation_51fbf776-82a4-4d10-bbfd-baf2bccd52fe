<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="<%= description || 'JooCourses - Professional Online Learning Platform for Tech Skills' %>">
  <meta name="keywords" content="<%= keywords || 'online courses, programming, web development, tech skills, learning platform' %>">
  <meta name="author" content="JooCourses">

  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="<%= title || 'JooCourses' %>">
  <meta property="og:description" content="<%= description || 'Professional Online Learning Platform for Tech Skills' %>">
  <meta property="og:type" content="website">
  <meta property="og:url" content="<%= typeof url !== 'undefined' ? url : 'https://joocourses.com' %>">
  <meta property="og:image" content="/images/og-image.jpg">

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="<%= title || 'JooCourses' %>">
  <meta name="twitter:description" content="<%= description || 'Professional Online Learning Platform for Tech Skills' %>">

  <title><%= title || 'JooCourses - Professional Online Learning Platform' %></title>

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
  <link rel="apple-touch-icon" href="/images/apple-touch-icon.png">

  <!-- CSS Files -->
  <link rel="stylesheet" href="/css/layouts.css">
  <link rel="stylesheet" href="/css/components.css">
  <% if (typeof additionalCSS !== 'undefined' && additionalCSS) { %>
    <% additionalCSS.forEach(function(css) { %>
      <link rel="stylesheet" href="<%= css %>">
    <% }); %>
  <% } %>

  <!-- Font Awesome for Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="<%= typeof bodyClass !== 'undefined' ? bodyClass : '' %>">
  <!-- Loading Spinner -->
  <div id="loading-spinner" class="loading-spinner">
    <div class="spinner"></div>
  </div>

  <!-- Skip to main content for accessibility -->
  <a href="#main-content" class="skip-link">Skip to main content</a>

  <!-- Header -->
  <%- include('../partials/header.ejs', {
    user: typeof user !== 'undefined' ? user : null,
    currentPage: typeof currentPage !== 'undefined' ? currentPage : ''
  }) %>

  <!-- Flash Messages -->
  <% if (typeof messages !== 'undefined' && messages) { %>
    <%- include('../partials/flash-messages.ejs', { messages: messages }) %>
  <% } %>

  <!-- Main Content -->
  <main id="main-content" class="main-content" role="main">
    <%- body %>
  </main>

  <!-- Footer -->
  <%- include('../partials/footer.ejs') %>

  <!-- JavaScript Files -->
  <script src="/js/main.js"></script>
  <% if (typeof additionalJS !== 'undefined' && additionalJS) { %>
    <% additionalJS.forEach(function(js) { %>
      <script src="<%= js %>"></script>
    <% }); %>
  <% } %>

  <!-- Page-specific JavaScript -->
  <% if (typeof inlineJS !== 'undefined' && inlineJS) { %>
    <script>
      <%- inlineJS %>
    </script>
  <% } %>
</body>
</html>
