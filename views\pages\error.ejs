<link rel="stylesheet" href="/css/error-pages.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<div class="error-page">
  <div class="error-content">
    <div class="error-icon">
      <i class="fas fa-exclamation-triangle"></i>
    </div>
    
    <div class="error-details">
      <h1 class="error-code">Error</h1>
      <h2 class="error-title">Something went wrong</h2>
      <p class="error-message">
        <%= typeof message !== 'undefined' ? message : 'An unexpected error occurred. Please try again later.' %>
      </p>
      
      <div class="error-actions">
        <a href="javascript:history.back()" class="btn btn-secondary">
          <i class="fas fa-arrow-left"></i>
          Go Back
        </a>
        <a href="/" class="btn btn-primary">
          <i class="fas fa-home"></i>
          Home Page
        </a>
        <button onclick="window.location.reload()" class="btn btn-outline">
          <i class="fas fa-redo"></i>
          Try Again
        </button>
      </div>
    </div>
  </div>
  
  <div class="error-illustration">
    <div class="warning-animation">
      <i class="fas fa-exclamation"></i>
    </div>
  </div>
</div>
