/**
 * @fileoverview Course controller - handles HTTP requests for course operations
 * @module controllers/courseController
 */

import * as courseService from '../Services/courseService.js';
import { sendResponse, sendCreated, sendSuccess, sendPaginated } from '../utils/sendResponse.js';
import { catchAsync } from '../utils/errors.js';

/**
 * Create a new course
 * @async
 * @function createCourse
 * @param {Object} req - Express request object
 * @param {Object} req.body - Request body containing course data
 * @param {string} req.body.title - Course title
 * @param {string} req.body.description - Course description
 * @param {string} req.body.level - Course level
 * @param {number} req.body.duration - Course duration in hours
 * @param {number} req.body.price - Course price
 * @param {Object} req.user - Authenticated user object
 * @param {string} req.user.userId - Instructor user ID
 * @param {Object} [req.file] - Uploaded image file
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 *
 * @example
 * POST /api/courses
 * Content-Type: multipart/form-data
 * {
 *   "title": "JavaScript Fundamentals",
 *   "description": "Learn JavaScript from scratch",
 *   "level": "Beginner",
 *   "duration": 20,
 *   "price": 99.99
 * }
 */
export const createCourse = catchAsync(async (req, res) => {
  const courseData = { ...req.body };
  courseData.instructor = req.user.userId || req.user._id;

  // Handle image upload
  if (req.file) {
    courseData.imageUrl = `/uploads/${req.file.filename}`;
  }

  const course = await courseService.createCourse(courseData);
  return sendCreated(res, 'Course created successfully', course);
});

/**
 * Get all published courses from ALL instructors with pagination and filters
 * @async
 * @function getAllCourses
 * @param {Object} req - Express request object
 * @param {Object} req.query - Query parameters
 * @param {number} [req.query.page=1] - Page number
 * @param {number} [req.query.limit=10] - Items per page
 * @param {string} [req.query.category] - Filter by category
 * @param {string} [req.query.level] - Filter by level
 * @param {string} [req.query.search] - Search term
 * @param {string} [req.query.tags] - Comma-separated tags
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 *
 * @description MAIN API FOR STUDENTS - Returns all published courses from all instructors.
 * This is the primary endpoint students use to browse and discover courses.
 * No authentication required - public access for everyone.
 *
 * @example
 * GET /api/courses - Get all courses from all instructors
 * GET /api/courses?page=1&limit=10&level=Beginner&search=javascript
 * GET /api/courses?category=Programming&level=Intermediate
 */
export const getAllCourses = catchAsync(async (req, res) => {
  const { page, limit, category, level, search, tags } = req.query;

  const options = {
    page: parseInt(page) || 1,
    limit: parseInt(limit) || 10,
    filters: {
      category,
      level,
      search,
      tags: tags ? tags.split(',') : undefined
    }
  };

  const result = await courseService.getAllCourses(options);

  return sendPaginated(res, 'Courses retrieved successfully', result.courses, result.pagination);
});

/**
 * Get course by ID
 * @async
 * @function getCourseById
 * @param {Object} req - Express request object
 * @param {Object} req.params - Route parameters
 * @param {string} req.params.id - Course ID
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 *
 * @example
 * GET /api/courses/:id
 */
export const getCourseById = catchAsync(async (req, res) => {
  const course = await courseService.getCourseById(req.params.id, true);

  if (!course) {
    return sendResponse(res, 404, 'Course not found');
  }

  return sendSuccess(res, 'Course fetched successfully', course);
});

/**
 * Update course by ID
 * @async
 * @function updateCourse
 * @param {Object} req - Express request object
 * @param {Object} req.params - Route parameters
 * @param {string} req.params.id - Course ID
 * @param {Object} req.body - Request body containing update data
 * @param {Object} req.user - Authenticated user object
 * @param {string} req.user.userId - Current user ID
 * @param {Object} [req.file] - Uploaded image file
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 *
 * @example
 * PUT /api/courses/:id
 * {
 *   "title": "Updated Course Title",
 *   "price": 149.99
 * }
 */
export const updateCourse = catchAsync(async (req, res) => {
  const updateData = { ...req.body };
  const instructorId = req.user.userId || req.user._id;

  // Handle image upload
  if (req.file) {
    updateData.imageUrl = `/uploads/${req.file.filename}`;
  }

  const updatedCourse = await courseService.updateCourse(req.params.id, updateData, instructorId);
  return sendSuccess(res, 'Course updated successfully', updatedCourse);
});

/**
 * Delete course by ID
 * @async
 * @function deleteCourse
 * @param {Object} req - Express request object
 * @param {Object} req.params - Route parameters
 * @param {string} req.params.id - Course ID
 * @param {Object} req.user - Authenticated user object
 * @param {string} req.user.userId - Current user ID
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 *
 * @example
 * DELETE /api/courses/:id
 */
export const deleteCourse = catchAsync(async (req, res) => {
  const instructorId = req.user.userId || req.user._id;

  await courseService.deleteCourse(req.params.id, instructorId);
  return sendSuccess(res, 'Course deleted successfully');
});

/**
 * Get courses for the logged-in instructor
 * @async
 * @function getMyCourses
 * @param {Object} req - Express request object
 * @param {Object} req.query - Query parameters
 * @param {number} [req.query.page=1] - Page number
 * @param {number} [req.query.limit=10] - Items per page
 * @param {string} [req.query.status] - Filter by status
 * @param {string} [req.query.category] - Filter by category
 * @param {string} [req.query.level] - Filter by level
 * @param {string} [req.query.search] - Search term
 * @param {Object} req.user - Authenticated user object
 * @param {string} req.user.userId - Current user ID
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 *
 * @example
 * GET /api/my-courses?page=1&limit=10&status=published
 */
export const getMyCourses = catchAsync(async (req, res) => {
  const instructorId = req.user.userId || req.user._id;
  const { page, limit, status, category, level, search } = req.query;

  const filters = { status, category, level, search };
  const result = await courseService.getCoursesByInstructor(
    instructorId,
    filters,
    parseInt(page) || 1,
    parseInt(limit) || 10
  );

  return sendPaginated(res, 'Instructor courses retrieved successfully', result.courses, result.pagination);
});
