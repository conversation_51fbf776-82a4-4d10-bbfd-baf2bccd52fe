/**
 * @fileoverview User controller - handles HTTP requests for user operations
 * @module controllers/userController
 */

import { sendResponse, sendCreated, sendSuccess } from '../utils/sendResponse.js';
import { catchAsync } from '../utils/errors.js';
import * as userService from '../Services/userService.js';

/**
 * Create a new user
 * @async
 * @function addUser
 * @param {Object} req - Express request object
 * @param {Object} req.body - Request body containing user data
 * @param {string} req.body.name - User's name
 * @param {string} req.body.email - User's email
 * @param {string} req.body.password - User's password
 * @param {string} [req.body.role] - User's role
 * @param {Object} [req.file] - Uploaded file object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 * @returns {Promise<void>}
 *
 * @example
 * POST /api/users
 * {
 *   "name": "<PERSON>",
 *   "email": "<EMAIL>",
 *   "password": "securePassword123",
 *   "role": "student"
 * }
 */
export const addUser = catchAsync(async (req, res) => {
  const { name, email, password, role } = req.body;
  const photo = req.file ? req.file.path : req.body.photo || '';

  const user = await userService.registerUser({
    name,
    email,
    password,
    role,
    photo
  });

  return sendCreated(res, 'User created successfully', user);
});

/**
 * Get all users with pagination and filtering
 * @async
 * @function showAllUsers
 * @param {Object} req - Express request object
 * @param {Object} req.query - Query parameters
 * @param {number} [req.query.page=1] - Page number
 * @param {number} [req.query.limit=10] - Items per page
 * @param {string} [req.query.role] - Filter by role
 * @param {string} [req.query.search] - Search term
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 *
 * @example
 * GET /api/users?page=1&limit=10&role=instructor&search=john
 */
export const showAllUsers = catchAsync(async (req, res) => {
  const { page, limit, role, search } = req.query;

  const result = await userService.getAllUsers({
    page: parseInt(page),
    limit: parseInt(limit),
    role,
    search
  });

  return sendSuccess(res, 'Users retrieved successfully', result.users, {
    pagination: result.pagination
  });
});

/**
 * Update user information
 * @async
 * @function updateUser
 * @param {Object} req - Express request object
 * @param {Object} req.params - Route parameters
 * @param {string} req.params.id - User ID
 * @param {Object} req.body - Request body containing update data
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 *
 * @example
 * PUT /api/users/:id
 * {
 *   "name": "Jane Doe",
 *   "email": "<EMAIL>"
 * }
 */
export const updateUser = catchAsync(async (req, res) => {
  const updatedUser = await userService.updateUser(req.params.id, req.body);
  return sendSuccess(res, 'User updated successfully', updatedUser);
});

/**
 * Get user by ID
 * @async
 * @function getUserById
 * @param {Object} req - Express request object
 * @param {Object} req.params - Route parameters
 * @param {string} req.params.id - User ID
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 *
 * @example
 * GET /api/users/:id
 */
export const getUserById = catchAsync(async (req, res) => {
  const user = await userService.getUserById(req.params.id);
  return sendSuccess(res, 'User retrieved successfully', user);
});

/**
 * Get current user profile
 * @async
 * @function profile
 * @param {Object} req - Express request object
 * @param {Object} req.user - Authenticated user object
 * @param {string} req.user.userId - Current user ID
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 *
 * @example
 * GET /api/users/profile
 * Authorization: Bearer <token>
 */
export const profile = catchAsync(async (req, res) => {
  const user = await userService.getUserById(req.user.userId, true);
  return sendSuccess(res, 'Profile retrieved successfully', user);
});

/**
 * Change user password
 * @async
 * @function changePassword
 * @param {Object} req - Express request object
 * @param {Object} req.user - Authenticated user object
 * @param {string} req.user.userId - Current user ID
 * @param {Object} req.body - Request body containing password data
 * @param {string} req.body.currentPassword - Current password
 * @param {string} req.body.newPassword - New password
 * @param {string} req.body.confirmPassword - Password confirmation
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 *
 * @example
 * PUT /api/users/change-password
 * {
 *   "currentPassword": "oldPassword123",
 *   "newPassword": "newPassword456",
 *   "confirmPassword": "newPassword456"
 * }
 */
export const changePassword = catchAsync(async (req, res) => {
  await userService.changePassword(req.user.userId, req.body);
  return sendSuccess(res, 'Password changed successfully');
});

/**
 * Delete user by ID
 * @async
 * @function deleteUser
 * @param {Object} req - Express request object
 * @param {Object} req.params - Route parameters
 * @param {string} req.params.id - User ID to delete
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 *
 * @example
 * DELETE /api/users/:id
 */
export const deleteUser = catchAsync(async (req, res) => {
  await userService.deleteUser(req.params.id);
  return sendSuccess(res, 'User deleted successfully');
});

/**
 * Update user profile photo
 * @async
 * @function updateImg
 * @param {Object} req - Express request object
 * @param {Object} req.user - Authenticated user object
 * @param {string} req.user.userId - Current user ID
 * @param {Object} req.file - Uploaded file object
 * @param {string} req.file.path - File path
 * @param {Object} res - Express response object
 * @returns {Promise<void>}
 *
 * @example
 * PUT /api/users/photo
 * Content-Type: multipart/form-data
 * photo: <file>
 */
export const updateImg = catchAsync(async (req, res) => {
  if (!req.file) {
    return sendResponse(res, 400, 'No image file provided');
  }

  const updatedUser = await userService.updateUserPhoto(req.user.userId, req.file.path);
  return sendSuccess(res, 'Profile photo updated successfully', updatedUser);
});