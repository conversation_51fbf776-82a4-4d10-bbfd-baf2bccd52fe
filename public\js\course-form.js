// Course Form JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeCourseForm();
});

function initializeCourseForm() {
    // Initialize form validation
    initializeValidation();
    
    // Initialize image upload
    initializeImageUpload();
    
    // Initialize tag input
    initializeTagInput();
    
    // Initialize character counters
    initializeCharacterCounters();
    
    // Initialize form auto-save
    initializeAutoSave();
    
    // Initialize prerequisites and learning outcomes
    initializeTextareaLists();
    
    // Initialize form submission
    initializeFormSubmission();
}

function initializeValidation() {
    const form = document.querySelector('.course-form');
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', clearFieldError);
    });
    
    // Real-time validation for specific fields
    const titleInput = document.getElementById('title');
    const priceInput = document.getElementById('price');
    const durationInput = document.getElementById('duration');
    
    if (titleInput) {
        titleInput.addEventListener('input', function() {
            if (this.value.length > 100) {
                showFieldError(this, 'Title cannot exceed 100 characters');
            }
        });
    }
    
    if (priceInput) {
        priceInput.addEventListener('input', function() {
            if (this.value < 0) {
                showFieldError(this, 'Price cannot be negative');
            }
        });
    }
    
    if (durationInput) {
        durationInput.addEventListener('input', function() {
            if (this.value < 0.5) {
                showFieldError(this, 'Duration must be at least 0.5 hours');
            } else if (this.value > 500) {
                showFieldError(this, 'Duration cannot exceed 500 hours');
            }
        });
    }
}

function validateField(e) {
    const field = e.target;
    const value = field.value.trim();
    
    // Clear previous errors
    clearFieldError(e);
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'This field is required');
        return false;
    }
    
    // Email validation
    if (field.type === 'email' && value && !isValidEmail(value)) {
        showFieldError(field, 'Please enter a valid email address');
        return false;
    }
    
    // URL validation
    if (field.type === 'url' && value && !isValidURL(value)) {
        showFieldError(field, 'Please enter a valid URL');
        return false;
    }
    
    return true;
}

function clearFieldError(e) {
    const field = e.target;
    const errorElement = field.parentNode.querySelector('.field-error');
    if (errorElement) {
        errorElement.remove();
    }
    field.classList.remove('error');
}

function showFieldError(field, message) {
    clearFieldError({ target: field });
    
    field.classList.add('error');
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.textContent = message;
    field.parentNode.appendChild(errorElement);
}

function initializeImageUpload() {
    const fileInput = document.getElementById('image');
    const uploadArea = document.querySelector('.upload-area');
    const previewContainer = document.querySelector('.current-image');
    
    if (!fileInput || !uploadArea) return;
    
    // Drag and drop functionality
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('drag-over');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('drag-over');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('drag-over');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelection(files[0]);
        }
    });
    
    // File input change
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelection(e.target.files[0]);
        }
    });
    
    function handleFileSelection(file) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            showMessage('error', 'Please select a valid image file (JPG, PNG, GIF)');
            return;
        }
        
        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            showMessage('error', 'Image size must be less than 5MB');
            return;
        }
        
        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            showImagePreview(e.target.result, file.name);
        };
        reader.readAsDataURL(file);
    }
    
    function showImagePreview(src, filename) {
        let previewImg = document.querySelector('.preview-image');
        let imageLabel = document.querySelector('.image-label');
        
        if (!previewImg) {
            // Create preview elements if they don't exist
            const currentImageDiv = document.querySelector('.current-image') || document.createElement('div');
            currentImageDiv.className = 'current-image';
            
            previewImg = document.createElement('img');
            previewImg.className = 'preview-image';
            
            imageLabel = document.createElement('p');
            imageLabel.className = 'image-label';
            
            currentImageDiv.appendChild(previewImg);
            currentImageDiv.appendChild(imageLabel);
            
            const imageUploadSection = document.querySelector('.image-upload-section');
            imageUploadSection.insertBefore(currentImageDiv, imageUploadSection.firstChild);
        }
        
        previewImg.src = src;
        imageLabel.textContent = `Selected: ${filename}`;
    }
}

function initializeTagInput() {
    const tagInput = document.getElementById('tags');
    if (!tagInput) return;
    
    tagInput.addEventListener('input', function() {
        const tags = this.value.split(',').map(tag => tag.trim()).filter(tag => tag);
        
        if (tags.length > 10) {
            showFieldError(this, 'Maximum 10 tags allowed');
        } else {
            clearFieldError({ target: this });
        }
    });
    
    // Add tag suggestions (future enhancement)
    const tagSuggestions = ['javascript', 'python', 'web development', 'react', 'node.js', 'css', 'html', 'programming', 'design', 'business'];
    
    // Create suggestions dropdown
    const suggestionsDiv = document.createElement('div');
    suggestionsDiv.className = 'tag-suggestions';
    suggestionsDiv.style.display = 'none';
    tagInput.parentNode.appendChild(suggestionsDiv);
    
    tagInput.addEventListener('focus', function() {
        showTagSuggestions();
    });
    
    tagInput.addEventListener('blur', function() {
        setTimeout(() => {
            suggestionsDiv.style.display = 'none';
        }, 200);
    });
    
    function showTagSuggestions() {
        const currentTags = tagInput.value.split(',').map(tag => tag.trim().toLowerCase());
        const availableTags = tagSuggestions.filter(tag => !currentTags.includes(tag));
        
        if (availableTags.length > 0) {
            suggestionsDiv.innerHTML = availableTags.slice(0, 5).map(tag => 
                `<div class="tag-suggestion" onclick="addTag('${tag}')">${tag}</div>`
            ).join('');
            suggestionsDiv.style.display = 'block';
        }
    }
    
    window.addTag = function(tag) {
        const currentValue = tagInput.value.trim();
        const newValue = currentValue ? `${currentValue}, ${tag}` : tag;
        tagInput.value = newValue;
        suggestionsDiv.style.display = 'none';
        tagInput.focus();
    };
}

function initializeCharacterCounters() {
    const fieldsWithCounters = [
        { id: 'title', max: 100 },
        { id: 'description', max: 2000 },
        { id: 'category', max: 50 }
    ];
    
    fieldsWithCounters.forEach(field => {
        const element = document.getElementById(field.id);
        if (!element) return;
        
        const counter = document.createElement('div');
        counter.className = 'character-counter';
        element.parentNode.appendChild(counter);
        
        function updateCounter() {
            const length = element.value.length;
            counter.textContent = `${length}/${field.max}`;
            counter.className = `character-counter ${length > field.max ? 'over-limit' : ''}`;
        }
        
        element.addEventListener('input', updateCounter);
        updateCounter(); // Initial count
    });
}

function initializeAutoSave() {
    const form = document.querySelector('.course-form');
    const inputs = form.querySelectorAll('input, textarea, select');
    let autoSaveTimeout;
    
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(autoSaveForm, 2000); // Auto-save after 2 seconds of inactivity
        });
    });
    
    function autoSaveForm() {
        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            if (key !== 'image') { // Don't auto-save file uploads
                data[key] = value;
            }
        }
        
        localStorage.setItem('courseFormAutoSave', JSON.stringify(data));
        showAutoSaveIndicator();
    }
    
    function showAutoSaveIndicator() {
        let indicator = document.querySelector('.auto-save-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'auto-save-indicator';
            document.body.appendChild(indicator);
        }
        
        indicator.textContent = 'Draft saved';
        indicator.style.display = 'block';
        
        setTimeout(() => {
            indicator.style.display = 'none';
        }, 2000);
    }
    
    // Restore auto-saved data on page load
    const savedData = localStorage.getItem('courseFormAutoSave');
    if (savedData) {
        try {
            const data = JSON.parse(savedData);
            Object.keys(data).forEach(key => {
                const element = form.querySelector(`[name="${key}"]`);
                if (element && !element.value) {
                    element.value = data[key];
                }
            });
        } catch (error) {
            console.error('Failed to restore auto-saved data:', error);
        }
    }
}

function initializeTextareaLists() {
    const prerequisitesTextarea = document.getElementById('prerequisites');
    const learningOutcomesTextarea = document.getElementById('learningOutcomes');
    
    [prerequisitesTextarea, learningOutcomesTextarea].forEach(textarea => {
        if (!textarea) return;
        
        textarea.addEventListener('input', function() {
            // Auto-format as list items
            const lines = this.value.split('\n');
            const formattedLines = lines.map(line => {
                const trimmed = line.trim();
                if (trimmed && !trimmed.startsWith('•') && !trimmed.startsWith('-')) {
                    return `• ${trimmed}`;
                }
                return line;
            });
            
            if (formattedLines.join('\n') !== this.value) {
                const cursorPos = this.selectionStart;
                this.value = formattedLines.join('\n');
                this.setSelectionRange(cursorPos + 2, cursorPos + 2);
            }
        });
    });
}

function initializeFormSubmission() {
    const form = document.querySelector('.course-form');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Validate form
        if (!validateForm()) {
            return;
        }
        
        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        submitBtn.disabled = true;
        
        // Process form data
        const formData = new FormData(form);
        
        // Convert textarea lists to arrays
        const prerequisites = formData.get('prerequisites');
        if (prerequisites) {
            const prereqArray = prerequisites.split('\n')
                .map(line => line.replace(/^[•\-]\s*/, '').trim())
                .filter(line => line);
            formData.delete('prerequisites');
            prereqArray.forEach(item => formData.append('prerequisites', item));
        }
        
        const learningOutcomes = formData.get('learningOutcomes');
        if (learningOutcomes) {
            const outcomesArray = learningOutcomes.split('\n')
                .map(line => line.replace(/^[•\-]\s*/, '').trim())
                .filter(line => line);
            formData.delete('learningOutcomes');
            outcomesArray.forEach(item => formData.append('learningOutcomes', item));
        }
        
        // Submit form
        fetch(form.action, {
            method: form.method,
            body: formData,
            credentials: 'include'
        })
        .then(response => {
            if (response.redirected) {
                // Clear auto-saved data on successful submission
                localStorage.removeItem('courseFormAutoSave');
                window.location.href = response.url;
            } else {
                return response.text();
            }
        })
        .then(html => {
            if (html) {
                // If we get HTML back, there was an error
                document.body.innerHTML = html;
                initializeCourseForm(); // Re-initialize
            }
        })
        .catch(error => {
            console.error('Form submission error:', error);
            showMessage('error', 'Failed to save course. Please try again.');
        })
        .finally(() => {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
}

function validateForm() {
    const form = document.querySelector('.course-form');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!validateField({ target: field })) {
            isValid = false;
        }
    });
    
    return isValid;
}

// Utility functions
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidURL(url) {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

function showMessage(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        ${message}
    `;
    
    const container = document.querySelector('.course-form-container');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Add additional CSS for form enhancements
const formStyles = document.createElement('style');
formStyles.textContent = `
    .field-error {
        color: #ff6b6b;
        font-size: 0.85rem;
        margin-top: 0.25rem;
    }
    
    .form-input.error,
    .form-textarea.error,
    .form-select.error {
        border-color: #ff6b6b;
        box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
    }
    
    .character-counter {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.6);
        text-align: right;
        margin-top: 0.25rem;
    }
    
    .character-counter.over-limit {
        color: #ff6b6b;
    }
    
    .tag-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(26, 26, 46, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        max-height: 150px;
        overflow-y: auto;
        z-index: 1000;
        backdrop-filter: blur(10px);
    }
    
    .tag-suggestion {
        padding: 0.5rem;
        cursor: pointer;
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.85rem;
        transition: background 0.3s ease;
    }
    
    .tag-suggestion:hover {
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
    }
    
    .auto-save-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(17, 153, 142, 0.9);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-size: 0.85rem;
        z-index: 1000;
        display: none;
    }
    
    .upload-area.drag-over {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
    }
`;

document.head.appendChild(formStyles);
