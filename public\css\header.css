* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    color: #e0e6ed;
}

header {
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    padding: 1rem 2rem;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    animation: slideDown 0.8s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

h1 {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(45deg,   #00d4ff, #7c3aed );
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
    position: relative;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

h1::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #00d4ff, #7c3aed );
    border-radius: 2px;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 10px rgba(0, 212, 255, 0.5); }
    to { box-shadow: 0 0 20px rgba(0, 212, 255, 0.8), 0 0 30px rgba(124, 58, 237, 0.3); }
}

/* Desktop Navigation */
.desktop-nav {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.desktop-nav a {
    color: #e0e6ed;
    text-decoration: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1rem;
    position: relative;
    background: rgba(255,255,255,0.05);
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.desktop-nav a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.desktop-nav a:hover::before {
    left: 100%;
}

.desktop-nav a:hover {
    transform: translateY(-2px);
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.3);
    box-shadow: 0 10px 25px rgba(0,0,0,0.4);
    color: #ffffff;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    background: rgba(255,255,255,0.05);
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: rgba(255,255,255,0.1);
    transform: scale(1.05);
}

.hamburger-line {
    width: 25px;
    height: 3px;
    background: #e0e6ed;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 3px;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
    background: #00d4ff;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
    background: #00d4ff;
}

/* Mobile Sidebar */
.mobile-sidebar {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: linear-gradient(180deg, rgba(15, 15, 35, 0.98) 0%, rgba(26, 26, 46, 0.98) 100%);
    backdrop-filter: blur(20px);
    border-left: 1px solid rgba(255,255,255,0.1);
    transition: right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 2000;
    padding: 2rem 1.5rem;
    box-shadow: -10px 0 30px rgba(0,0,0,0.5);
}

.mobile-sidebar.active {
    right: 0;
}

.mobile-sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.mobile-sidebar h2 {
    color: #e0e6ed;
    font-size: 1.5rem;
    background: linear-gradient(45deg, #00d4ff, #7c3aed);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.close-sidebar {
    background: none;
    border: none;
    color: #e0e6ed;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-sidebar:hover {
    background: rgba(255,255,255,0.1);
    transform: rotate(90deg);
}

.mobile-nav {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mobile-nav a {
    color: #e0e6ed;
    text-decoration: none;
    padding: 1rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1.1rem;
    background: rgba(255,255,255,0.05);
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mobile-nav a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.mobile-nav a:hover::before {
    left: 100%;
}

.mobile-nav a:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.3);
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

/* Color-coded navigation items */
.nav-item-home:hover { border-color: #ef4444; box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3); }
.nav-item-about:hover { border-color: #3b82f6; box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3); }
.nav-item-contact:hover { border-color: #10b981; box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3); }
.nav-item-courses:hover { border-color: #f59e0b; box-shadow: 0 5px 15px rgba(245, 158, 11, 0.3); }
.nav-item-login:hover { border-color: #8b5cf6; box-shadow: 0 5px 15px rgba(139, 92, 246, 0.3); }
.nav-item-register:hover { border-color: #f97316; box-shadow: 0 5px 15px rgba(249, 115, 22, 0.3); }

/* Overlay */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1500;
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Responsive Design */
@media (max-width: 768px) {
    header {
        padding: 1rem 1.5rem;
    }

    .desktop-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    h1 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    header {
        padding: 1rem;
    }

    h1 {
        font-size: 1.8rem;
    }

    .mobile-sidebar {
        width: 280px;
    }
}

/* Demo content */
.demo-content {
    padding: 4rem 2rem;
    text-align: center;
    color: #e0e6ed;
}

.demo-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #00d4ff, #7c3aed);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.demo-content p {
    font-size: 1.2rem;
    opacity: 0.8;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}