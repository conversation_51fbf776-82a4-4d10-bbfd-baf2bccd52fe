<!-- Course Browsing Page -->
<div class="courses-browse-page">
  <!-- Hero Section -->
  <section class="courses-hero">
    <div class="container">
      <div class="hero-content">
        <h1 class="hero-title">Discover Amazing Courses</h1>
        <p class="hero-description">
          Learn from expert instructors and advance your career with hands-on projects and real-world skills
        </p>
        
        <!-- Search Bar -->
        <div class="hero-search">
          <div class="search-container">
            <i class="fas fa-search search-icon"></i>
            <input 
              type="text" 
              id="courseSearch" 
              class="search-input" 
              placeholder="What do you want to learn today?"
            >
            <button class="search-btn" onclick="searchCourses()">
              <span>Search</span>
            </button>
          </div>
        </div>
        
        <!-- Popular Tags -->
        <div class="popular-tags">
          <span class="tags-label">Popular:</span>
          <button class="tag-btn" onclick="filterByTag('javascript')">JavaScript</button>
          <button class="tag-btn" onclick="filterByTag('python')">Python</button>
          <button class="tag-btn" onclick="filterByTag('react')">React</button>
          <button class="tag-btn" onclick="filterByTag('nodejs')">Node.js</button>
          <button class="tag-btn" onclick="filterByTag('machine-learning')">Machine Learning</button>
        </div>
      </div>
    </div>
  </section>

  <!-- Filters and Results -->
  <section class="courses-content">
    <div class="container">
      <div class="content-layout">
        <!-- Sidebar Filters -->
        <aside class="filters-sidebar">
          <div class="filters-header">
            <h3>Filter Courses</h3>
            <button class="clear-filters" onclick="clearAllFilters()">Clear All</button>
          </div>
          
          <div class="filter-group">
            <h4 class="filter-title">Category</h4>
            <div class="filter-options">
              <label class="filter-option">
                <input type="checkbox" name="category" value="Programming">
                <span class="checkmark"></span>
                <span class="option-text">Programming</span>
                <span class="option-count">(45)</span>
              </label>
              <label class="filter-option">
                <input type="checkbox" name="category" value="Web Development">
                <span class="checkmark"></span>
                <span class="option-text">Web Development</span>
                <span class="option-count">(32)</span>
              </label>
              <label class="filter-option">
                <input type="checkbox" name="category" value="Data Science">
                <span class="checkmark"></span>
                <span class="option-text">Data Science</span>
                <span class="option-count">(28)</span>
              </label>
              <label class="filter-option">
                <input type="checkbox" name="category" value="Mobile Development">
                <span class="checkmark"></span>
                <span class="option-text">Mobile Development</span>
                <span class="option-count">(18)</span>
              </label>
              <label class="filter-option">
                <input type="checkbox" name="category" value="DevOps">
                <span class="checkmark"></span>
                <span class="option-text">DevOps</span>
                <span class="option-count">(15)</span>
              </label>
            </div>
          </div>
          
          <div class="filter-group">
            <h4 class="filter-title">Level</h4>
            <div class="filter-options">
              <label class="filter-option">
                <input type="radio" name="level" value="Beginner">
                <span class="checkmark radio"></span>
                <span class="option-text">Beginner</span>
              </label>
              <label class="filter-option">
                <input type="radio" name="level" value="Intermediate">
                <span class="checkmark radio"></span>
                <span class="option-text">Intermediate</span>
              </label>
              <label class="filter-option">
                <input type="radio" name="level" value="Advanced">
                <span class="checkmark radio"></span>
                <span class="option-text">Advanced</span>
              </label>
            </div>
          </div>
          
          <div class="filter-group">
            <h4 class="filter-title">Price</h4>
            <div class="filter-options">
              <label class="filter-option">
                <input type="radio" name="price" value="free">
                <span class="checkmark radio"></span>
                <span class="option-text">Free</span>
              </label>
              <label class="filter-option">
                <input type="radio" name="price" value="paid">
                <span class="checkmark radio"></span>
                <span class="option-text">Paid</span>
              </label>
            </div>
          </div>
          
          <div class="filter-group">
            <h4 class="filter-title">Duration</h4>
            <div class="duration-range">
              <input type="range" id="durationRange" min="0.5" max="100" value="50" class="range-slider">
              <div class="range-labels">
                <span>0.5h</span>
                <span id="durationValue">50h</span>
                <span>100h+</span>
              </div>
            </div>
          </div>
        </aside>
        
        <!-- Main Content -->
        <main class="courses-main">
          <!-- Results Header -->
          <div class="results-header">
            <div class="results-info">
              <h2 id="resultsCount">Loading courses...</h2>
              <p class="results-subtitle">Find the perfect course for your learning goals</p>
            </div>
            
            <div class="results-controls">
              <div class="sort-dropdown">
                <select id="sortBy" class="sort-select">
                  <option value="newest">Newest First</option>
                  <option value="popular">Most Popular</option>
                  <option value="rating">Highest Rated</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                </select>
              </div>
              
              <div class="view-toggle">
                <button class="view-btn active" data-view="grid" title="Grid View">
                  <i class="fas fa-th"></i>
                </button>
                <button class="view-btn" data-view="list" title="List View">
                  <i class="fas fa-list"></i>
                </button>
              </div>
            </div>
          </div>
          
          <!-- Courses Grid -->
          <div class="courses-container">
            <div id="coursesGrid" class="courses-grid">
              <!-- Loading skeleton -->
              <div class="loading-skeleton">
                <div class="skeleton-card">
                  <div class="skeleton-image"></div>
                  <div class="skeleton-content">
                    <div class="skeleton-title"></div>
                    <div class="skeleton-text"></div>
                    <div class="skeleton-text"></div>
                    <div class="skeleton-actions"></div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Load More Button -->
            <div class="load-more-container" id="loadMoreContainer" style="display: none;">
              <button class="btn btn-outline btn-large" id="loadMoreBtn" onclick="loadMoreCourses()">
                <span>Load More Courses</span>
                <i class="fas fa-chevron-down"></i>
              </button>
            </div>
            
            <!-- Pagination -->
            <div class="pagination-container" id="paginationContainer">
              <!-- Pagination will be loaded here -->
            </div>
          </div>
        </main>
      </div>
    </div>
  </section>
</div>

<!-- Mobile Filter Modal -->
<div class="modal" id="mobileFiltersModal">
  <div class="modal-content">
    <div class="modal-header">
      <h3 class="modal-title">Filter Courses</h3>
      <button class="modal-close" onclick="closeMobileFilters()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="modal-body">
      <!-- Mobile filters content (same as sidebar) -->
      <div class="mobile-filters">
        <!-- Filters will be cloned here for mobile -->
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn btn-outline" onclick="clearAllFilters()">Clear All</button>
      <button class="btn btn-primary" onclick="applyMobileFilters()">Apply Filters</button>
    </div>
  </div>
</div>

<!-- Mobile Filter Toggle -->
<button class="mobile-filter-toggle" onclick="openMobileFilters()">
  <i class="fas fa-filter"></i>
  <span>Filters</span>
</button>

<script>
// Global variables
let currentCourses = [];
let currentPage = 1;
let totalPages = 1;
let isLoading = false;
let currentFilters = {};

// Load courses with filters
async function loadCourses(page = 1, append = false) {
  if (isLoading) return;
  
  try {
    isLoading = true;
    showLoading();
    
    const params = new URLSearchParams({
      page: page,
      limit: 12,
      ...currentFilters
    });
    
    const response = await fetch(`/courses/api/courses?${params}`);
    const data = await response.json();
    
    if (data.success) {
      if (append) {
        currentCourses = [...currentCourses, ...data.data];
      } else {
        currentCourses = data.data;
      }
      
      currentPage = data.meta.pagination.currentPage;
      totalPages = data.meta.pagination.totalPages;
      
      renderCourses(currentCourses, append);
      updateResultsCount(data.meta.pagination.totalItems);
      updatePagination();
    } else {
      showError('Failed to load courses');
    }
  } catch (error) {
    console.error('Error loading courses:', error);
    showError('Failed to load courses');
  } finally {
    isLoading = false;
    hideLoading();
  }
}

// Render courses
function renderCourses(courses, append = false) {
  const container = document.getElementById('coursesGrid');
  const viewMode = document.querySelector('.view-btn.active').dataset.view;
  
  if (!append) {
    container.innerHTML = '';
    container.className = `courses-${viewMode}`;
  }
  
  if (courses.length === 0 && !append) {
    container.innerHTML = `
      <div class="empty-state">
        <i class="fas fa-search"></i>
        <h3>No courses found</h3>
        <p>Try adjusting your filters or search terms</p>
        <button class="btn btn-primary" onclick="clearAllFilters()">Clear Filters</button>
      </div>
    `;
    return;
  }
  
  const coursesHTML = courses.map(course => `
    <div class="course-card" data-aos="fade-up">
      <div class="course-image">
        <img src="${course.imageUrl || '/images/course-placeholder.jpg'}" alt="${course.title}">
        <div class="course-level">${course.level}</div>
        <div class="course-wishlist">
          <button class="wishlist-btn" onclick="toggleWishlist('${course._id}')">
            <i class="far fa-heart"></i>
          </button>
        </div>
      </div>
      <div class="course-content">
        <div class="course-category">${course.category}</div>
        <h3 class="course-title">${course.title}</h3>
        <p class="course-description">${course.description.substring(0, 120)}...</p>
        
        <div class="course-instructor">
          <img src="${course.instructor.photo || '/images/default-avatar.jpg'}" alt="${course.instructor.name}">
          <span>${course.instructor.name}</span>
        </div>
        
        <div class="course-meta">
          <div class="course-rating">
            <div class="stars">
              ${generateStars(course.rating || 0)}
            </div>
            <span class="rating-text">${course.rating || 0} (${course.reviewCount || 0})</span>
          </div>
          <div class="course-stats">
            <span><i class="fas fa-clock"></i> ${course.duration}h</span>
            <span><i class="fas fa-users"></i> ${course.enrollmentCount || 0}</span>
          </div>
        </div>
        
        <div class="course-footer">
          <div class="course-price">
            ${course.price === 0 ? '<span class="free">Free</span>' : `<span class="price">$${course.price}</span>`}
          </div>
          <a href="/courses/${course._id}" class="btn btn-primary btn-small">
            <span>View Course</span>
          </a>
        </div>
      </div>
    </div>
  `).join('');
  
  if (append) {
    container.insertAdjacentHTML('beforeend', coursesHTML);
  } else {
    container.innerHTML = coursesHTML;
  }
  
  // Initialize AOS for new elements
  if (typeof AOS !== 'undefined') {
    AOS.refresh();
  }
}

// Search functionality
function searchCourses() {
  const searchTerm = document.getElementById('courseSearch').value.trim();
  currentFilters.search = searchTerm;
  currentPage = 1;
  loadCourses(1);
}

// Filter functionality
function applyFilters() {
  const filters = {};
  
  // Category filters
  const categories = Array.from(document.querySelectorAll('input[name="category"]:checked'))
    .map(cb => cb.value);
  if (categories.length > 0) {
    filters.category = categories.join(',');
  }
  
  // Level filter
  const level = document.querySelector('input[name="level"]:checked');
  if (level) {
    filters.level = level.value;
  }
  
  // Price filter
  const price = document.querySelector('input[name="price"]:checked');
  if (price) {
    filters.price = price.value;
  }
  
  // Duration filter
  const duration = document.getElementById('durationRange').value;
  if (duration < 100) {
    filters.maxDuration = duration;
  }
  
  currentFilters = { ...currentFilters, ...filters };
  currentPage = 1;
  loadCourses(1);
}

// Utility functions
function generateStars(rating) {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  return '★'.repeat(fullStars) + 
         (hasHalfStar ? '☆' : '') + 
         '☆'.repeat(emptyStars);
}

function updateResultsCount(total) {
  document.getElementById('resultsCount').textContent = `${total} courses found`;
}

function showLoading() {
  // Implementation for loading state
}

function hideLoading() {
  // Implementation for hiding loading state
}

function showError(message) {
  console.error(message);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
  loadCourses();
  
  // Search on Enter key
  document.getElementById('courseSearch').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      searchCourses();
    }
  });
  
  // Filter change listeners
  document.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach(input => {
    input.addEventListener('change', applyFilters);
  });
  
  // Duration range slider
  document.getElementById('durationRange').addEventListener('input', function() {
    document.getElementById('durationValue').textContent = this.value + 'h';
  });
  
  // Sort change
  document.getElementById('sortBy').addEventListener('change', function() {
    currentFilters.sort = this.value;
    loadCourses(1);
  });
  
  // View toggle
  document.querySelectorAll('.view-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
      this.classList.add('active');
      renderCourses(currentCourses);
    });
  });
});
</script>
