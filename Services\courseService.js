/**
 * @fileoverview Course service layer - handles all course-related business logic
 * @module services/courseService
 */

import Course from '../models/coursemodel.js';
import User from '../models/usermodel.js';
import { 
  ValidationError, 
  NotFoundError, 
  AuthorizationError 
} from '../utils/errors.js';
import { 
  validateCourseData, 
  isValidObjectId 
} from '../utils/validation.js';
import { 
  calculatePagination, 
  processTags, 
  processTextList,
  generateSlug 
} from '../utils/helpers.js';
import { COURSE_STATUS, USER_ROLES } from '../utils/constants.js';

/**
 * Create a new course
 * @async
 * @function createCourse
 * @param {Object} courseData - Course data
 * @param {string} courseData.title - Course title
 * @param {string} courseData.description - Course description
 * @param {string} courseData.instructor - Instructor user ID
 * @param {string} courseData.level - Course level
 * @param {number} courseData.duration - Course duration in hours
 * @param {number} [courseData.price=0] - Course price
 * @param {string} [courseData.category] - Course category
 * @param {string|Array} [courseData.tags] - Course tags
 * @param {string} [courseData.status=draft] - Course status
 * @returns {Promise<Object>} Created course object
 * @throws {ValidationError} When course data is invalid
 * @throws {NotFoundError} When instructor is not found
 * 
 * @example
 * const course = await createCourse({
 *   title: 'JavaScript Fundamentals',
 *   description: 'Learn JavaScript from scratch',
 *   instructor: '60d5ecb54b24a1234567890a',
 *   level: 'Beginner',
 *   duration: 20,
 *   price: 99.99
 * });
 */
export const createCourse = async (courseData) => {
  try {
    // Validate course data
    validateCourseData(courseData);

    // Verify instructor exists and has correct role
    if (!isValidObjectId(courseData.instructor)) {
      throw new ValidationError('Invalid instructor ID format');
    }

    const instructor = await User.findById(courseData.instructor);
    if (!instructor) {
      throw new NotFoundError('Instructor not found');
    }

    if (instructor.role !== USER_ROLES.INSTRUCTOR) {
      throw new ValidationError('User must be an instructor to create courses');
    }

    // Process course data
    const processedData = {
      ...courseData,
      tags: processTags(courseData.tags),
      status: courseData.status || COURSE_STATUS.DRAFT,
      slug: generateSlug(courseData.title)
    };

    // Process prerequisites and learning outcomes if provided
    if (courseData.prerequisites) {
      processedData.prerequisites = processTextList(courseData.prerequisites);
    }

    if (courseData.learningOutcomes) {
      processedData.learningOutcomes = processTextList(courseData.learningOutcomes);
    }

    // Create course
    const course = await Course.create(processedData);
    
    // Populate instructor data
    await course.populate('instructor', 'name email photo');
    
    return course;
  } catch (error) {
    if (error.name === 'ValidationError') {
      throw new ValidationError(error.message);
    }
    throw error;
  }
};

/**
 * Get all published courses from ALL instructors with pagination and filters
 * @async
 * @function getAllCourses
 * @param {Object} [options={}] - Query options
 * @param {number} [options.page=1] - Page number
 * @param {number} [options.limit=10] - Items per page
 * @param {Object} [options.filters={}] - Filter options
 * @param {string} [options.filters.category] - Filter by category
 * @param {string} [options.filters.level] - Filter by level
 * @param {Array} [options.filters.tags] - Filter by tags
 * @param {string} [options.filters.search] - Search term
 * @returns {Promise<Object>} Paginated courses list from ALL instructors
 *
 * @description This function returns ALL published courses from ALL instructors in the system.
 * It's the main API for students to browse and discover courses. Only published and public
 * courses are returned, ensuring students see only available content.
 *
 * @example
 * // Get all courses from all instructors
 * const result = await getAllCourses({
 *   page: 1,
 *   limit: 10,
 *   filters: { level: 'Beginner', search: 'javascript' }
 * });
 *
 * // Result includes courses from multiple instructors
 * console.log(result.courses); // Array of courses from different instructors
 * console.log(result.pagination); // Pagination info
 */
export const getAllCourses = async (options = {}) => {
  const { page = 1, limit = 10, filters = {} } = options;
  const pagination = calculatePagination(0, page, limit);

  // Build query for published courses only
  const query = { 
    status: COURSE_STATUS.PUBLISHED, 
    isPublic: true 
  };

  // Apply filters
  if (filters.category) query.category = filters.category;
  if (filters.level) query.level = filters.level;
  if (filters.tags && filters.tags.length > 0) {
    query.tags = { $in: filters.tags };
  }
  if (filters.search) {
    query.$or = [
      { title: { $regex: filters.search, $options: 'i' } },
      { description: { $regex: filters.search, $options: 'i' } }
    ];
  }

  // Execute query with pagination
  const [courses, total] = await Promise.all([
    Course.find(query)
      .populate('instructor', 'name photo')
      .skip(pagination.skip)
      .limit(pagination.limit)
      .sort({ createdAt: -1 })
      .lean(),
    Course.countDocuments(query)
  ]);

  // Calculate final pagination
  const finalPagination = calculatePagination(total, page, limit);

  return {
    courses,
    pagination: finalPagination
  };
};

/**
 * Get courses by instructor with filters and pagination
 * @async
 * @function getCoursesByInstructor
 * @param {string} instructorId - Instructor user ID
 * @param {Object} [filters={}] - Filter options
 * @param {string} [filters.status] - Filter by status
 * @param {string} [filters.category] - Filter by category
 * @param {string} [filters.level] - Filter by level
 * @param {string} [filters.search] - Search term
 * @param {number} [page=1] - Page number
 * @param {number} [limit=10] - Items per page
 * @returns {Promise<Object>} Paginated instructor courses
 * @throws {ValidationError} When instructor ID is invalid
 * 
 * @example
 * const result = await getCoursesByInstructor('60d5ecb54b24a1234567890a', 
 *   { status: 'published' }, 1, 10);
 */
export const getCoursesByInstructor = async (instructorId, filters = {}, page = 1, limit = 10) => {
  if (!isValidObjectId(instructorId)) {
    throw new ValidationError('Invalid instructor ID format');
  }

  const pagination = calculatePagination(0, page, limit);
  const query = { instructor: instructorId };

  // Apply filters
  if (filters.status) query.status = filters.status;
  if (filters.category) query.category = filters.category;
  if (filters.level) query.level = filters.level;
  if (filters.search) {
    query.$or = [
      { title: { $regex: filters.search, $options: 'i' } },
      { description: { $regex: filters.search, $options: 'i' } }
    ];
  }

  // Execute query with pagination
  const [courses, total] = await Promise.all([
    Course.find(query)
      .skip(pagination.skip)
      .limit(pagination.limit)
      .sort({ createdAt: -1 })
      .lean(),
    Course.countDocuments(query)
  ]);

  // Calculate final pagination
  const finalPagination = calculatePagination(total, page, limit);

  return {
    courses,
    pagination: finalPagination
  };
};

/**
 * Get course by ID
 * @async
 * @function getCourseById
 * @param {string} courseId - Course ID
 * @param {boolean} [populateInstructor=false] - Whether to populate instructor data
 * @returns {Promise<Object|null>} Course object or null if not found
 * @throws {ValidationError} When course ID is invalid
 * 
 * @example
 * const course = await getCourseById('60d5ecb54b24a1234567890a', true);
 */
export const getCourseById = async (courseId, populateInstructor = false) => {
  if (!isValidObjectId(courseId)) {
    throw new ValidationError('Invalid course ID format');
  }

  const query = Course.findById(courseId);
  
  if (populateInstructor) {
    query.populate('instructor', 'name email photo');
  }
  
  return query.lean();
};

/**
 * Update course by ID
 * @async
 * @function updateCourse
 * @param {string} courseId - Course ID
 * @param {Object} updateData - Data to update
 * @param {string} [instructorId] - Instructor ID for ownership validation
 * @returns {Promise<Object>} Updated course object
 * @throws {ValidationError} When course data is invalid
 * @throws {NotFoundError} When course is not found
 * @throws {AuthorizationError} When user doesn't own the course
 * 
 * @example
 * const updatedCourse = await updateCourse('60d5ecb54b24a1234567890a', {
 *   title: 'Updated Course Title',
 *   price: 149.99
 * }, instructorId);
 */
export const updateCourse = async (courseId, updateData, instructorId = null) => {
  if (!isValidObjectId(courseId)) {
    throw new ValidationError('Invalid course ID format');
  }

  // Validate update data
  if (Object.keys(updateData).length > 0) {
    validateCourseData(updateData);
  }

  // Get existing course
  const existingCourse = await Course.findById(courseId);
  if (!existingCourse) {
    throw new NotFoundError('Course not found');
  }

  // Check ownership if instructor ID is provided
  if (instructorId && existingCourse.instructor.toString() !== instructorId.toString()) {
    throw new AuthorizationError('You do not have permission to update this course');
  }

  // Process update data
  const processedData = { ...updateData };
  
  if (updateData.tags) {
    processedData.tags = processTags(updateData.tags);
  }
  
  if (updateData.prerequisites) {
    processedData.prerequisites = processTextList(updateData.prerequisites);
  }
  
  if (updateData.learningOutcomes) {
    processedData.learningOutcomes = processTextList(updateData.learningOutcomes);
  }

  // Update course
  const updatedCourse = await Course.findByIdAndUpdate(
    courseId,
    processedData,
    { new: true, runValidators: true }
  );

  return updatedCourse;
};

/**
 * Delete course by ID
 * @async
 * @function deleteCourse
 * @param {string} courseId - Course ID
 * @param {string} [instructorId] - Instructor ID for ownership validation
 * @returns {Promise<void>}
 * @throws {ValidationError} When course ID is invalid
 * @throws {NotFoundError} When course is not found
 * @throws {AuthorizationError} When user doesn't own the course
 *
 * @example
 * await deleteCourse('60d5ecb54b24a1234567890a', instructorId);
 */
export const deleteCourse = async (courseId, instructorId = null) => {
  if (!isValidObjectId(courseId)) {
    throw new ValidationError('Invalid course ID format');
  }

  // Get existing course
  const existingCourse = await Course.findById(courseId);
  if (!existingCourse) {
    throw new NotFoundError('Course not found');
  }

  // Check ownership if instructor ID is provided
  if (instructorId && existingCourse.instructor.toString() !== instructorId.toString()) {
    throw new AuthorizationError('You do not have permission to delete this course');
  }

  await Course.findByIdAndDelete(courseId);
};

/**
 * Get instructor dashboard statistics
 * @async
 * @function getInstructorStats
 * @param {string} instructorId - Instructor user ID
 * @returns {Promise<Object>} Instructor statistics
 * @throws {ValidationError} When instructor ID is invalid
 *
 * @example
 * const stats = await getInstructorStats('60d5ecb54b24a1234567890a');
 */
export const getInstructorStats = async (instructorId) => {
  if (!isValidObjectId(instructorId)) {
    throw new ValidationError('Invalid instructor ID format');
  }

  const stats = await Course.aggregate([
    { $match: { instructor: instructorId } },
    {
      $group: {
        _id: null,
        totalCourses: { $sum: 1 },
        publishedCourses: {
          $sum: { $cond: [{ $eq: ['$status', COURSE_STATUS.PUBLISHED] }, 1, 0] }
        },
        draftCourses: {
          $sum: { $cond: [{ $eq: ['$status', COURSE_STATUS.DRAFT] }, 1, 0] }
        },
        totalStudents: { $sum: '$enrollmentCount' },
        totalRevenue: {
          $sum: { $multiply: ['$price', '$enrollmentCount'] }
        },
        averageRating: { $avg: '$rating' }
      }
    }
  ]);

  return stats[0] || {
    totalCourses: 0,
    publishedCourses: 0,
    draftCourses: 0,
    totalStudents: 0,
    totalRevenue: 0,
    averageRating: 0
  };
};

/**
 * Get recent courses for instructor
 * @async
 * @function getRecentCourses
 * @param {string} instructorId - Instructor user ID
 * @param {number} [limit=5] - Number of courses to return
 * @returns {Promise<Array>} Array of recent courses
 * @throws {ValidationError} When instructor ID is invalid
 *
 * @example
 * const recentCourses = await getRecentCourses('60d5ecb54b24a1234567890a', 5);
 */
export const getRecentCourses = async (instructorId, limit = 5) => {
  if (!isValidObjectId(instructorId)) {
    throw new ValidationError('Invalid instructor ID format');
  }

  return Course.find({ instructor: instructorId })
    .sort({ createdAt: -1 })
    .limit(limit)
    .select('title status createdAt enrollmentCount rating')
    .lean();
};

/**
 * Validate course ownership
 * @async
 * @function validateCourseOwnership
 * @param {string} courseId - Course ID
 * @param {string} instructorId - Instructor user ID
 * @returns {Promise<Object>} Course object if ownership is valid
 * @throws {ValidationError} When IDs are invalid
 * @throws {NotFoundError} When course is not found
 * @throws {AuthorizationError} When user doesn't own the course
 *
 * @example
 * const course = await validateCourseOwnership(courseId, instructorId);
 */
export const validateCourseOwnership = async (courseId, instructorId) => {
  if (!isValidObjectId(courseId)) {
    throw new ValidationError('Invalid course ID format');
  }

  if (!isValidObjectId(instructorId)) {
    throw new ValidationError('Invalid instructor ID format');
  }

  const course = await Course.findById(courseId).select('instructor');
  if (!course) {
    throw new NotFoundError('Course not found');
  }

  if (course.instructor.toString() !== instructorId.toString()) {
    throw new AuthorizationError('You do not have permission to access this course');
  }

  return course;
};

/**
 * Get course categories used by instructor
 * @async
 * @function getInstructorCategories
 * @param {string} instructorId - Instructor user ID
 * @returns {Promise<Array>} Array of unique categories
 * @throws {ValidationError} When instructor ID is invalid
 *
 * @example
 * const categories = await getInstructorCategories('60d5ecb54b24a1234567890a');
 */
export const getInstructorCategories = async (instructorId) => {
  if (!isValidObjectId(instructorId)) {
    throw new ValidationError('Invalid instructor ID format');
  }

  const categories = await Course.distinct('category', {
    instructor: instructorId,
    category: { $ne: null, $ne: '' }
  });

  return categories;
};

/**
 * Bulk update course status
 * @async
 * @function bulkUpdateCourseStatus
 * @param {Array} courseIds - Array of course IDs
 * @param {string} status - New status
 * @param {string} instructorId - Instructor user ID
 * @returns {Promise<Object>} Update result
 * @throws {ValidationError} When data is invalid
 *
 * @example
 * const result = await bulkUpdateCourseStatus(['id1', 'id2'], 'published', instructorId);
 */
export const bulkUpdateCourseStatus = async (courseIds, status, instructorId) => {
  if (!Array.isArray(courseIds) || courseIds.length === 0) {
    throw new ValidationError('Course IDs array is required');
  }

  if (!Object.values(COURSE_STATUS).includes(status)) {
    throw new ValidationError('Invalid course status');
  }

  if (!isValidObjectId(instructorId)) {
    throw new ValidationError('Invalid instructor ID format');
  }

  // Validate all course IDs
  for (const courseId of courseIds) {
    if (!isValidObjectId(courseId)) {
      throw new ValidationError(`Invalid course ID format: ${courseId}`);
    }
  }

  return Course.updateMany(
    {
      _id: { $in: courseIds },
      instructor: instructorId
    },
    { status },
    { runValidators: true }
  );
};
