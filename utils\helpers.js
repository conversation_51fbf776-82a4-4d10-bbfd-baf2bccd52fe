/**
 * @fileoverview Helper utility functions
 * @module utils/helpers
 */

import crypto from 'crypto';
import { PAGINATION_DEFAULTS } from './constants.js';

/**
 * Generate a random string
 * @param {number} [length=32] - Length of the random string
 * @returns {string} Random string
 * 
 * @example
 * const resetToken = generateRandomString(64);
 */
export const generateRandomString = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * Hash a string using SHA256
 * @param {string} input - String to hash
 * @returns {string} Hashed string
 * 
 * @example
 * const hashedToken = hashString(resetToken);
 */
export const hashString = (input) => {
  return crypto.createHash('sha256').update(input).digest('hex');
};

/**
 * Calculate pagination metadata
 * @param {number} total - Total number of items
 * @param {number} page - Current page number
 * @param {number} limit - Items per page
 * @returns {Object} Pagination metadata
 * 
 * @example
 * const pagination = calculatePagination(100, 2, 10);
 * // Returns: { page: 2, limit: 10, total: 100, totalPages: 10, hasNext: true, hasPrev: true }
 */
export const calculatePagination = (total, page = PAGINATION_DEFAULTS.PAGE, limit = PAGINATION_DEFAULTS.LIMIT) => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    page: parseInt(page),
    limit: parseInt(limit),
    total: parseInt(total),
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1,
    skip: (page - 1) * limit
  };
};

/**
 * Format date to ISO string
 * @param {Date|string} [date=new Date()] - Date to format
 * @returns {string} ISO formatted date string
 * 
 * @example
 * const formattedDate = formatDate(new Date());
 */
export const formatDate = (date = new Date()) => {
  return new Date(date).toISOString();
};

/**
 * Remove sensitive fields from user object
 * @param {Object} user - User object
 * @param {string[]} [fieldsToRemove=['password', 'resetPasswordToken', 'resetPasswordExpires']] - Fields to remove
 * @returns {Object} Sanitized user object
 * 
 * @example
 * const safeUser = sanitizeUser(user);
 */
export const sanitizeUser = (user, fieldsToRemove = ['password', 'resetPasswordToken', 'resetPasswordExpires']) => {
  if (!user) return null;
  
  const sanitized = { ...user };
  
  // Handle Mongoose documents
  if (user.toObject && typeof user.toObject === 'function') {
    const userObj = user.toObject();
    fieldsToRemove.forEach(field => {
      delete userObj[field];
    });
    return userObj;
  }
  
  // Handle plain objects
  fieldsToRemove.forEach(field => {
    delete sanitized[field];
  });
  
  return sanitized;
};

/**
 * Process tags from string or array
 * @param {string|Array} tags - Tags to process
 * @returns {Array} Processed tags array
 * 
 * @example
 * const processedTags = processTags('javascript, react, node.js');
 * // Returns: ['javascript', 'react', 'node.js']
 */
export const processTags = (tags) => {
  if (!tags) return [];
  
  if (typeof tags === 'string') {
    return tags
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
      .slice(0, 10); // Limit to 10 tags
  }
  
  if (Array.isArray(tags)) {
    return tags
      .map(tag => String(tag).trim())
      .filter(tag => tag.length > 0)
      .slice(0, 10);
  }
  
  return [];
};

/**
 * Process text list from textarea (prerequisites, learning outcomes)
 * @param {string} text - Text with line breaks
 * @returns {Array} Array of processed items
 * 
 * @example
 * const outcomes = processTextList('• Learn JavaScript\n• Build web apps\n• Master React');
 * // Returns: ['Learn JavaScript', 'Build web apps', 'Master React']
 */
export const processTextList = (text) => {
  if (!text || typeof text !== 'string') return [];
  
  return text
    .split('\n')
    .map(line => line.replace(/^[•\-\*]\s*/, '').trim()) // Remove bullet points
    .filter(line => line.length > 0);
};

/**
 * Generate slug from title
 * @param {string} title - Title to convert to slug
 * @returns {string} URL-friendly slug
 * 
 * @example
 * const slug = generateSlug('Learn JavaScript Programming');
 * // Returns: 'learn-javascript-programming'
 */
export const generateSlug = (title) => {
  if (!title || typeof title !== 'string') return '';
  
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

/**
 * Calculate reading time for text content
 * @param {string} text - Text content
 * @param {number} [wordsPerMinute=200] - Average reading speed
 * @returns {number} Reading time in minutes
 * 
 * @example
 * const readingTime = calculateReadingTime(courseDescription);
 */
export const calculateReadingTime = (text, wordsPerMinute = 200) => {
  if (!text || typeof text !== 'string') return 0;
  
  const wordCount = text.trim().split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
};

/**
 * Validate and format file upload data
 * @param {Object} file - Multer file object
 * @param {string[]} allowedTypes - Allowed MIME types
 * @param {number} maxSize - Maximum file size in bytes
 * @returns {Object} Formatted file data
 * @throws {Error} When file validation fails
 * 
 * @example
 * const fileData = validateFileUpload(req.file, ['image/jpeg', 'image/png'], 5242880);
 */
export const validateFileUpload = (file, allowedTypes, maxSize) => {
  if (!file) {
    throw new Error('No file provided');
  }
  
  if (!allowedTypes.includes(file.mimetype)) {
    throw new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`);
  }
  
  if (file.size > maxSize) {
    throw new Error(`File too large. Maximum size: ${Math.round(maxSize / 1024 / 1024)}MB`);
  }
  
  return {
    filename: file.filename,
    originalName: file.originalname,
    mimetype: file.mimetype,
    size: file.size,
    path: file.path,
    url: `/uploads/${file.filename}`
  };
};

/**
 * Deep clone an object
 * @param {*} obj - Object to clone
 * @returns {*} Cloned object
 * 
 * @example
 * const clonedUser = deepClone(user);
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const cloned = {};
    Object.keys(obj).forEach(key => {
      cloned[key] = deepClone(obj[key]);
    });
    return cloned;
  }
};

/**
 * Check if value is empty (null, undefined, empty string, empty array, empty object)
 * @param {*} value - Value to check
 * @returns {boolean} True if value is empty
 * 
 * @example
 * if (isEmpty(user.bio)) {
 *   user.bio = 'No bio provided';
 * }
 */
export const isEmpty = (value) => {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim().length === 0;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

/**
 * Debounce function execution
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 * 
 * @example
 * const debouncedSearch = debounce(searchFunction, 300);
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Format file size in human readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 * 
 * @example
 * const size = formatFileSize(1024000); // Returns: "1.02 MB"
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
